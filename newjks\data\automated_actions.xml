<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

    <!-- Server Action: x_assign Description from Task Template -->
    <record id="server_action_x_assign_description_from_task_template" model="ir.actions.server">
      <field name="name">x_assign Description from Task Template</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="state">code</field>
      <field name="code">
desc_flag = False
if (not record.description) or record.description != '\n':
    desc_flag = True
if record.x_task_template and desc_flag:
    record.write({'description': record.x_task_template.x_description})
      </field>
    </record>

    <!-- Automated Action: x_assign Description from Task Template -->
    <record id="automation_x_assign_description_from_task_template" model="base.automation">
      <field name="name">x_assign Description from Task Template</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="trigger">on_create_or_write</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_assign_description_from_task_template')])]"/>
    </record>

    <!-- Server Action: x_Send Emails from Task -->
    <record id="server_action_x_send_emails_from_task" model="ir.actions.server">
      <field name="name">x_Send Emails from Task</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="state">code</field>
      <field name="code">
# Placeholder for custom email logic
# Example: record.message_post(body="Notification triggered")
      </field>
    </record>

    <!-- Automated Action: x_Send Emails from Task -->
    <record id="automation_x_send_emails_from_task" model="base.automation">
      <field name="name">x_Send Emails from Task</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="trigger">on_create_or_write</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_send_emails_from_task')])]"/>
    </record>

    <!-- Server Action: x_Get Default Pricing -->
    <record id="server_action_x_get_default_pricing" model="ir.actions.server">
      <field name="name">x_Get Default Pricing</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="state">code</field>
      <field name="code">
if not record.x_billing_amt != 0:
    if record.partner_id and record.project_id:
        if record.project_id.x_product_id:
            line = env['account.move.line'].search([
                ('move_id.move_type','=','out_invoice'),
                ('partner_id','=',record.partner_id.id),
                ('product_id','=',record.project_id.x_product_id.id)
            ], order='create_date DESC', limit=1)
            if line:
                record.write({'x_billing_amt': line.price_unit})
            else:
                record.write({'x_billing_amt': record.project_id.x_product_id.lst_price})
      </field>
    </record>

    <!-- Automated Action: x_Get Default Pricing -->
    <record id="automation_x_get_default_pricing" model="base.automation">
      <field name="name">x_Get Default Pricing</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="trigger">on_create</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_get_default_pricing')])]"/>
    </record>

    <!-- Server Action: x_check emails for task -->
    <record id="server_action_x_check_emails_for_task" model="ir.actions.server">
      <field name="name">x_check emails for task</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="state">code</field>
      <field name="code">
if record.x_user_assigned:
    emails = ''
    for u in record.x_user_assigned:
        emails += u.email + ','
    emails = emails[:-1]
    record.write({'x_emails': emails})
      </field>
    </record>

    <!-- Automated Action: x_check emails for task -->
    <record id="automation_x_check_emails_for_task" model="base.automation">
      <field name="name">x_check emails for task</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="trigger">on_create_or_write</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_check_emails_for_task')])]"/>
    </record>

    <!-- Server Action: x_set approved by in Task -->
    <record id="server_action_x_set_approved_by_in_task" model="ir.actions.server">
      <field name="name">x_set approved by in Task</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="state">code</field>
      <field name="code">
if not record.x_approved_by:
    record.write({'x_approved_by': record.create_uid.id})
      </field>
    </record>

    <!-- Automated Action: x_set approved by in Task -->
    <record id="automation_x_set_approved_by_in_task" model="base.automation">
      <field name="name">x_set approved by in Task</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="trigger">on_create</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_set_approved_by_in_task')])]"/>
    </record>

    <!-- Server Action: x_set Timesheets Automatically -->
    <record id="server_action_x_set_timesheets_automatically" model="ir.actions.server">
      <field name="name">x_set Timesheets Automatically</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="state">code</field>
      <field name="code">
tasks = env['task.checklist'].search([('x_project_id','=',record.project_id.id)], order='name ASC')
if not record.project_id:
    raise UserError("Kindly Assign Project")
if not record.x_user_assigned:
    raise UserError("Kindly Assign User to the Task")
user = None
for r in record.x_user_assigned:
    user = r
    break
empl = user.employee_id
to_date = datetime.date.today()
tuple_data = [(0, 0, {
    'date': to_date,
    'employee_id': empl.id,
    'name': check.name,
    'project_id': record.project_id.id
}) for check in tasks]
record.sudo().write({'timesheet_ids': tuple_data})
      </field>
    </record>

    <!-- Automated Action: x_set Timesheets Automatically -->
    <record id="automation_x_set_timesheets_automatically" model="base.automation">
      <field name="name">x_set Timesheets Automatically</field>
      <field name="model_id" ref="project.model_project_task"/>
      <field name="trigger">on_create</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_set_timesheets_automatically')])]"/>
    </record>

    <!-- Server Action: x_Timesheet Days Calculation -->
    <record id="server_action_x_timesheet_days_check" model="ir.actions.server">
      <field name="name">x_Timesheet Days Calculation</field>
      <field name="model_id" ref="hr_timesheet.model_account_analytic_line"/>
      <field name="state">code</field>
      <field name="code">
if not env.user.has_group('hr_timesheet.group_hr_timesheet_approver'):
    todayte = datetime.date.today() - datetime.timedelta(days=40)
    if record.date and record.date &lt;= todayte:
        raise UserError("You can not post Timesheet older than 40 days")
      </field>
    </record>

    <!-- Automated Action: x_Timesheet Days Calculation -->
    <record id="automation_x_timesheet_days_check" model="base.automation">
      <field name="name">x_Timesheet Days Calculation</field>
      <field name="model_id" ref="hr_timesheet.model_account_analytic_line"/>
      <field name="trigger">on_create_or_write</field>
      <field name="action_server_ids" eval="[(6, 0, [ref('server_action_x_timesheet_days_check')])]"/>
    </record>

    </data>
</odoo>
