# -*- coding: utf-8 -*-
{
    'name': 'Product Cigarette & Tobacco Classification',
    'version': '********.4',
    'category': 'Sales',
    'summary': 'Category-based product classification for cigars and tobacco with sorting functionality (cigarettes not implemented)',
    'description': """
Product Category-Based Classification
=====================================

This module adds:
* Category-based classification for products (cigars, tobacco, disposable)
* Sorting functionality for sales orders and invoices by product type
* PDF reports with products grouped and sorted by type

Features:
---------
* Category-based product classification (no custom fields needed)
* Automatic classification based on product category hierarchy
* Non-intrusive sorting - works with existing custom fields and views
* Helper methods to get sorted order/invoice lines by product type
* Manual sorting methods available: sort_lines_by_product_type()
* Invoice and Sales Order PDF reports show products grouped and sorted by type (premium cigars first, then tobacco, then non tobacco, then disposable)
* Easy product classification through category assignment
* Proper category detection using category hierarchy (cigar, tobacco, disposable)
* No new database fields - preserves existing customizations
* Server actions for easy sorting via Action menu

Note: Cigarette classification is planned but not yet implemented in this version.
    """,
    'author': 'Arihantai',
    'website': 'https://www.arihantai.com/',
    'depends': [
        'base',
        'product',
        'sale',
        'account',
    ],
    'data': [
        'data/server_actions.xml',
        'reports/invoice_report.xml',
        'reports/sale_report.xml',
    ],

    'installable': True,
    'auto_install': False,
    'application': False,
}