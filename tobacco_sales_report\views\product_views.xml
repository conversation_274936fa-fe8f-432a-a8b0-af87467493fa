<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Product Template Form View Extension -->
        <record id="product_template_form_view_cigar_fields" model="ir.ui.view">
            <field name="name">product.template.form.cigar.fields</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='general_information']//group[@name='group_general']" position="after">
                    <group string="Cigar Configuration" name="cigar_config">
                        <field name="x_cigars_per_box" 
                               help="Number of individual cigars in this box/pack. Used for automatic stick count calculation."/>
                        <field name="x_sticks_per_unit" 
                               help="Alternative field: Number of individual sticks/cigars per unit."/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- Product Product Form View Extension -->
        <record id="product_normal_form_view_cigar_fields" model="ir.ui.view">
            <field name="name">product.product.form.cigar.fields</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_normal_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='general_information']//group[@name='group_general']" position="after">
                    <group string="Cigar Configuration" name="cigar_config">
                        <field name="x_cigars_per_box" 
                               help="Number of individual cigars in this box/pack. Used for automatic stick count calculation."/>
                        <field name="x_sticks_per_unit" 
                               help="Alternative field: Number of individual sticks/cigars per unit."/>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
