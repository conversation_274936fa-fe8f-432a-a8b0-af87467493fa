/* Omnisol Website Custom Styles */

/* Root Variables */
:root {
    --omnisol-primary: #2563eb;
    --omnisol-secondary: #64748b;
    --omnisol-warning: #f59e0b;
    --omnisol-success: #10b981;
    --omnisol-danger: #ef4444;
    --omnisol-dark: #1e293b;
    --omnisol-light: #f8fafc;
    --omnisol-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --omnisol-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Global Styles */
body {
    font-family: var(--omnisol-font-family);
    line-height: 1.6;
    color: #334155;
}

/* Header Styles */
.omnisol-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e2e8f0;
}

.omnisol-logo {
    transition: transform 0.3s ease;
}

.omnisol-logo:hover {
    transform: scale(1.05);
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--omnisol-primary);
    margin-left: 0.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: #475569;
    transition: color 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--omnisol-primary);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background-color: var(--omnisol-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.omnisol-hero {
    background: var(--omnisol-gradient);
    position: relative;
    overflow: hidden;
}

.omnisol-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.omnisol-hero .container {
    position: relative;
    z-index: 2;
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-buttons .btn {
    font-weight: 600;
    border-radius: 50px;
    padding: 12px 30px;
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hero-stats h3 {
    font-size: 2rem;
    margin-bottom: 0.25rem;
}

/* Page Header */
.omnisol-page-header {
    background: var(--omnisol-gradient);
    position: relative;
}

.bg-gradient-primary {
    background: var(--omnisol-gradient) !important;
}

/* Feature Cards */
.feature-card {
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--omnisol-primary);
}

.feature-icon i {
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon i {
    transform: scale(1.1);
}

/* Product Cards */
.product-card {
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-showcase {
    padding: 2rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.product-showcase:last-child {
    border-bottom: none;
}

.product-info {
    padding: 2rem;
}

.specifications ul li {
    padding: 0.25rem 0;
    font-size: 0.95rem;
}

.pricing {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--omnisol-primary);
}

/* Category Cards */
.category-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--omnisol-primary);
}

/* Comparison Table */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table th {
    background: var(--omnisol-primary);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e2e8f0;
}

.table-hover tbody tr:hover {
    background-color: #f8fafc;
}

/* Contact Form */
.contact-form {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    margin-top: 0.25rem;
    font-size: 1.2rem;
}

/* Footer */
.omnisol-footer {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--omnisol-primary);
    transform: translateY(-2px);
}

/* Buttons */
.btn-primary {
    background: var(--omnisol-primary);
    border-color: var(--omnisol-primary);
    font-weight: 600;
}

.btn-primary:hover {
    background: #1d4ed8;
    border-color: #1d4ed8;
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--omnisol-warning);
    border-color: var(--omnisol-warning);
    color: white;
    font-weight: 600;
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
    color: white;
    transform: translateY(-1px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .omnisol-hero {
        text-align: center;
    }
    
    .hero-stats {
        margin-top: 2rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .product-showcase .row {
        flex-direction: column-reverse;
    }
    
    .product-showcase .col-lg-6.order-lg-2 {
        order: 1;
    }
    
    .product-showcase .col-lg-6.order-lg-1 {
        order: 2;
    }
}

/* Utility Classes */
.text-primary {
    color: var(--omnisol-primary) !important;
}

.bg-primary {
    background-color: var(--omnisol-primary) !important;
}

.border-primary {
    border-color: var(--omnisol-primary) !important;
}

.shadow-lg {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.rounded-3 {
    border-radius: 0.5rem !important;
}
