# -*- coding: utf-8 -*-
"""
Account Move models for cigarette and tobacco classification and sorting.

This module extends the account move (invoice) and account move line models to provide
product classification and sorting functionality for tobacco-related products.
It includes methods for grouping invoice lines by product type and sorting them
according to business priorities.
"""

from odoo import models


class AccountMove(models.Model):
    """
    Extends account.move to add product classification and sorting functionality.

    This class provides methods to group and sort invoice lines based on product
    categories (cigars, cigarettes, tobacco, disposable, others) for better
    organization in reports and invoice displays.
    """
    _inherit = 'account.move'

    def get_invoice_lines_by_type(self):
        """
        Return invoice lines grouped by product type for reports and analysis.

        This method categorizes all invoice lines into predefined product types
        and sorts them alphabetically within each category. It's primarily used
        for generating organized invoice reports and displaying products in a structured way.

        Returns:
            dict: Dictionary with product type keys and sorted line lists as values:
                - 'cigars': Lines containing cigar products
                - 'cigarettes': Lines containing cigarette products
                - 'tobacco': Lines containing tobacco products
                - 'disposable': Lines containing disposable products
                - 'others': Lines with non-tobacco products or no product
        """
        # Initialize dictionary to hold lines grouped by product type
        # NOTE: Cigarettes category is commented out as it's not implemented yet
        lines_by_type = {
            'cigars': [],
            # 'cigarettes': [],  # NOT IMPLEMENTED YET
            'tobacco': [],
            'others': [],
            'disposable': []
        }

        # Iterate through all invoice lines and categorize them
        for line in self.invoice_line_ids:
            if line.product_id:
                # Classify lines with products based on category
                if line.product_id.is_cigar_category():
                    lines_by_type['cigars'].append(line)
                # elif line.product_id.is_cigarette_category():
                #     lines_by_type['cigarettes'].append(line)  # NOT IMPLEMENTED YET
                elif line.product_id.is_tobacco_category():
                    lines_by_type['tobacco'].append(line)
                elif line.product_id.is_disposable_category():
                    lines_by_type['disposable'].append(line)
                else:
                    # Non-tobacco products go to 'others'
                    lines_by_type['others'].append(line)
            else:
                # Lines without products (services, etc.) go to 'others'
                lines_by_type['others'].append(line)

        # Sort lines within each group alphabetically by product name
        for group in lines_by_type:
            lines_by_type[group] = sorted(lines_by_type[group],
                                        key=lambda l: l.product_id.name if l.product_id else l.name or '')

        return lines_by_type

    def get_sorted_invoice_lines(self):
        """
        Return invoice lines sorted by product type priority, then by product name.

        This method provides a complete sorted list of invoice lines following
        the business priority: cigars first, then cigarettes, tobacco, others,
        and disposable products last. Within each category, products are sorted
        alphabetically.

        Returns:
            recordset: Sorted invoice lines with proper business priority ordering
        """
        def sort_key(line):
            """
            Generate sort key for invoice line.

            The sort key is a tuple that ensures proper ordering:
            1. Product type priority (1-6)
            2. Product name (alphabetical)
            3. Original sequence number
            4. Line ID (for consistency)
            """
            if line.product_id:
                # Use product's sort key, name, sequence, and ID
                return (line.product_id.get_product_sort_key(), line.product_id.name, line.sequence, line.id)
            else:
                # Non-product lines get priority 6 (lowest)
                return (6, line.name or '', line.sequence, line.id)

        # Return sorted recordset using the sort key function
        return self.invoice_line_ids.sorted(key=sort_key)

    def sort_lines_by_product_type(self):
        """
        Sort invoice lines by product type by updating their sequence numbers.

        This method permanently reorders the lines in the invoice by updating
        their sequence field. It's typically called from server actions or
        user interface buttons to reorganize the invoice display.

        Returns:
            dict: Action dictionary for displaying notification to user
        """
        # Check if there are any lines to sort
        if not self.invoice_line_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lines to Sort',
                    'message': 'This invoice has no lines to sort.',
                    'type': 'warning'
                }
            }

        # Get sorted lines and update their sequence numbers
        sorted_lines = self.get_sorted_invoice_lines()
        for index, line in enumerate(sorted_lines):
            # Use increments of 10 for sequence to allow manual reordering
            line.sequence = (index + 1) * 10

        # Return success notification
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Lines Sorted Successfully',
                'message': f'Sorted {len(sorted_lines)} invoice lines by product type.',
                'type': 'success'
            }
        }


class AccountMoveLine(models.Model):
    """
    Extends account.move.line to add product classification functionality.

    This class provides methods to determine the sort priority of individual
    invoice lines based on their associated product's category classification.
    """
    _inherit = 'account.move.line'

    def get_product_sort_key(self):
        """
        Return sort key for this line based on product type classification.

        This method delegates to the product's sort key method if a product
        is associated with the line, or returns a default value for non-product
        lines (such as services, discounts, taxes, or manual entries).

        Returns:
            int: Sort key (1-6) where lower numbers have higher priority:
                1 = Premium Cigars
                2 = Cigarettes
                3 = Tobacco Products
                4 = Non Tobacco Products
                5 = Disposable Products
                6 = Non-product lines (services, taxes, etc.)
        """
        if self.product_id:
            # Delegate to product's sort key method
            return self.product_id.get_product_sort_key()
        else:
            # Non-product lines (services, taxes, discounts, etc.) have lowest priority
            return 6
