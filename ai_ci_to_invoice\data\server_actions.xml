<?xml version="1.0" encoding="utf-8"?>
<!--
Server Actions for Product Cigarette & Tobacco Classification Module

This file defines server actions that provide user-accessible functionality
to sort order and invoice lines by product type. These actions appear in
the Action menu of sales orders and invoices, allowing users to easily
reorganize line items according to business priorities.

The sorting order follows tobacco industry priorities:
1. Premium Cigars (highest priority)
2. Cigarettes
3. Tobacco Products
4. Non Tobacco Products
5. Disposable Products (lowest priority)
-->
<odoo>
    <data>
        <!--
        Server Action for Sales Orders

        This action allows users to sort sales order lines by product type
        directly from the sales order form or list view. It appears in the
        Action menu and calls the sort_lines_by_product_type() method.
        -->
        <record id="action_sort_sale_order_lines" model="ir.actions.server">
            <field name="name">🔄 Sort Lines by Product Type</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="binding_model_id" ref="sale.model_sale_order"/>
            <field name="binding_view_types">list,form</field>
            <field name="state">code</field>
            <field name="code">
# Sort sales order lines by product type priority
# This code iterates through selected records and sorts their lines
# according to the tobacco product classification hierarchy
for record in records:
    if record.order_line:
        # Call the sorting method which updates line sequences
        # and displays a notification to the user
        record.sort_lines_by_product_type()
            </field>
        </record>

        <!--
        Server Action for Invoices

        This action allows users to sort invoice lines by product type
        directly from the invoice form or list view. It appears in the
        Action menu and calls the sort_lines_by_product_type() method.
        Only applies to customer and vendor invoices/refunds.
        -->
        <record id="action_sort_invoice_lines" model="ir.actions.server">
            <field name="name">🔄 Sort Lines by Product Type</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list,form</field>
            <field name="state">code</field>
            <field name="code">
# Sort invoice lines by product type priority
# This code iterates through selected records and sorts their lines
# according to the tobacco product classification hierarchy
for record in records:
    # Only process actual invoices and refunds (not journal entries)
    if record.type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund') and record.invoice_line_ids:
        # Call the sorting method which updates line sequences
        # and displays a notification to the user
        record.sort_lines_by_product_type()
            </field>
        </record>
    </data>
</odoo>
