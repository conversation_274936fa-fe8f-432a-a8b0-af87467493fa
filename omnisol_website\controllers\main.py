# -*- coding: utf-8 -*-
"""
Main controllers for Omnisol Website.
"""

from odoo import http
from odoo.http import request


class OmnisolWebsite(http.Controller):
    """Main controller for Omnisol website pages."""

    @http.route('/', type='http', auth="public", website=True)
    def home(self, **kwargs):
        """Render the home page."""
        return request.render('omnisol_website.home_page', {
            'page_name': 'home',
        })

    @http.route('/products', type='http', auth="public", website=True)
    def products(self, **kwargs):
        """Render the products page."""
        return request.render('omnisol_website.product_page', {
            'page_name': 'products',
        })

    @http.route('/about', type='http', auth="public", website=True)
    def about(self, **kwargs):
        """Render the about page."""
        return request.render('omnisol_website.about_page', {
            'page_name': 'about',
        })

    @http.route('/contact', type='http', auth="public", website=True)
    def contact(self, **kwargs):
        """Render the contact page."""
        return request.render('omnisol_website.contact_page', {
            'page_name': 'contact',
        })
