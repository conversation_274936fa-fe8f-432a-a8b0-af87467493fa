# Stick Count Functionality Guide

## Overview
The stick count feature allows salespersons to manually enter the actual number of individual cigar sticks being sold, which may differ from the calculated quantity based on boxes/packs.

## How It Works

### 1. Database Fields Created
- **`x_cigar_stick_count`** field added to `sale.order.line`
- **`x_cigar_stick_count`** field added to `account.move.line`
- Fields are created via `data/ir_model_fields.xml` for proper persistence

### 2. User Interface
- Field appears in sale order line items (after Quantity field)
- Field appears in invoice line items (after Quantity field)
- Field is labeled "Cigar Sticks" with helpful tooltip

### 3. Auto-Calculation Logic
When a cigar product is selected, the system:
1. Checks for custom fields on the product:
   - `x_sticks_per_unit`
   - `x_stick_count`
   - `x_pieces_per_box`
   - `x_cigars_per_box`
2. Calculates: `quantity × sticks_per_unit`
3. Only auto-fills if current value is 0 (allows manual override)

### 4. Data Transfer
- Stick count automatically transfers from sale order lines to invoice lines
- Manual adjustments can be made on invoices if needed

## Usage Examples

### Example 1: Standard Box Sale
- Product: "Premium Cigars Box of 20"
- Customer orders: 2 boxes
- **Quantity**: 2
- **Cigar Stick Count**: 40 (auto-calculated or manually entered)

### Example 2: Partial Box Sale
- Product: "Premium Cigars Box of 20"
- Customer wants: 15 individual cigars
- **Quantity**: 0.75 (or 1 if selling whole box)
- **Cigar Stick Count**: 15 (manually entered)

### Example 3: Mixed Sale
- Product: "Premium Cigars Box of 10"
- Customer orders: 3 boxes but only wants 25 sticks
- **Quantity**: 3
- **Cigar Stick Count**: 25 (manually entered)

## Excel Report Output

The monthly tobacco sales report shows:
- **Cigar Quantity (Boxes/Packs)**: The quantity field value
- **Premium Cigar Stick Count**: The actual individual cigars sold
- This provides clear distinction for regulatory and business reporting

## Troubleshooting

### If Stick Count Field Doesn't Appear:
1. **Check field creation**: Go to Settings > Technical > Database Structure > Fields
2. **Search for**: `x_cigar_stick_count`
3. **Should see two records**: One for `sale.order.line` and one for `account.move.line`

### If Auto-Calculation Doesn't Work:
1. **Check product setup**: Add custom fields to cigar products
2. **Field names to use**: `x_sticks_per_unit`, `x_stick_count`, `x_pieces_per_box`, or `x_cigars_per_box`
3. **Set values**: Number of sticks per unit (e.g., 20 for a box of 20)

### If Data Doesn't Transfer to Invoices:
1. **Check sale order**: Ensure stick count is entered on sale order lines
2. **Create invoice**: Use "Create Invoice" from sale order
3. **Verify transfer**: Check invoice lines for stick count values

## Technical Notes

- Fields are created as `integer` type for whole numbers
- Fields have `store=True` for database persistence
- Fields are indexed for better performance in reports
- Auto-calculation only triggers on product/quantity changes
- Manual values always take precedence over auto-calculation

## Integration with Reports

The tobacco sales Excel report uses these fields to provide:
- Accurate stick count reporting for regulatory compliance
- Clear separation between package quantity and individual item count
- Detailed sales analysis for business intelligence
