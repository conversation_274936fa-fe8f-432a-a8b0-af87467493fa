# Field Setup Guide for Cigar Stick Count Calculation

## Overview
This guide explains how to set up the required custom fields in Odoo Studio for automatic cigar stick count calculation. The fields must be created manually in Studio to avoid interference with core Odoo functionality.

## Required Fields to Create in Odoo Studio

### 1. Product Fields (for Cigar Products Only)

#### Field 1: x_cigars_per_box
- **Model**: `product.template` and `product.product`
- **Field Name**: `x_cigars_per_box`
- **Field Label**: `Cigars per Box`
- **Field Type**: `Integer`
- **Help Text**: `Number of individual cigars in this box/pack. Used for automatic stick count calculation.`
- **Default Value**: `0`

#### Field 2: x_sticks_per_unit (Alternative)
- **Model**: `product.template` and `product.product`
- **Field Name**: `x_sticks_per_unit`
- **Field Label**: `Sticks per Unit`
- **Field Type**: `Integer`
- **Help Text**: `Number of individual sticks/cigars per unit. Used for automatic stick count calculation.`
- **Default Value**: `0`

### 2. Sale Order Line Field

#### Field: x_cigar_stick_count
- **Model**: `sale.order.line`
- **Field Name**: `x_cigar_stick_count`
- **Field Label**: `Cigar Sticks`
- **Field Type**: `Integer`
- **Help Text**: `Number of individual cigar sticks being sold. Auto-calculated based on product configuration but can be manually overridden.`
- **Default Value**: `0`

### 3. Invoice Line Field

#### Field: x_cigar_stick_count
- **Model**: `account.move.line`
- **Field Name**: `x_cigar_stick_count`
- **Field Label**: `Cigar Sticks`
- **Field Type**: `Integer`
- **Help Text**: `Number of individual cigar sticks being sold. Transfers from sale order lines.`
- **Default Value**: `0`

## How Automatic Calculation Works

### 1. Product Configuration
For each cigar product, set one of these fields:
- `x_cigars_per_box`: Number of cigars in the box/pack
- `x_sticks_per_unit`: Alternative field name for the same purpose

### 2. Automatic Calculation Logic
When a user selects a cigar product and enters a quantity on a sale order line:

1. **Product Check**: System checks if the product belongs to cigar category
2. **Field Priority**: Looks for custom fields in this order:
   - `x_sticks_per_unit`
   - `x_stick_count`
   - `x_pieces_per_box`
   - `x_cigars_per_box`
3. **Calculation**: `Quantity × Sticks_per_unit = Total Stick Count`
4. **Auto-fill**: Only fills if current stick count is 0 (allows manual override)

### 3. Manual Override
Users can always manually change the calculated stick count value.

### 4. Data Transfer
Stick count automatically transfers from sale order lines to invoice lines when creating invoices.

## Example Usage

### Example 1: Box of 20 Cigars
- **Product**: "Premium Cigars Box of 20"
- **Product Setup**: `x_cigars_per_box = 20`
- **Sale Order**: Customer orders 3 boxes
- **Quantity**: 3
- **Auto-calculated Stick Count**: 3 × 20 = 60 cigars

### Example 2: Manual Override
- **Product**: "Premium Cigars Box of 25"
- **Product Setup**: `x_cigars_per_box = 25`
- **Sale Order**: Customer orders 2 boxes but only wants 30 sticks
- **Quantity**: 2
- **Auto-calculated**: 2 × 25 = 50
- **Manual Override**: User changes to 30

## Steps to Create Fields in Odoo Studio

1. **Go to Settings** → **Technical** → **Database Structure** → **Fields**
2. **Click Create** to add a new field
3. **Fill in the details** as specified above
4. **Save** the field
5. **Repeat** for all required fields

## Verification

After creating the fields, verify:
1. Fields appear in product forms for cigar products
2. Stick count field appears in sale order lines
3. Stick count field appears in invoice lines
4. Automatic calculation works when selecting cigar products
5. Manual override is possible
6. Data transfers from sale orders to invoices

## Troubleshooting

### If Auto-calculation Doesn't Work:
1. Check that the product is classified as a cigar category
2. Verify the custom fields are created on the product
3. Ensure the field values are set (not 0)
4. Check that the onchange method is working

### If Fields Don't Appear:
1. Verify field creation in Settings → Technical → Database Structure → Fields
2. Check field names match exactly (case-sensitive)
3. Refresh the browser/clear cache
4. Check user permissions

## Integration with Reports

The tobacco sales report uses these fields to:
- Show accurate stick counts for regulatory compliance
- Distinguish between box quantities and individual cigar counts
- Provide detailed sales analysis for cigar products
