from odoo import api, fields, models

class ProjectTask(models.Model):
    _inherit = "project.task"

    x_approved_by = fields.Many2one("res.users", string="Approved By", copy=True)
    x_billing_amt = fields.Float(string="Billing Amount", copy=True)
    x_date_of_month = fields.Integer(string="Date of month", copy=True)
    x_emails = fields.Char(string="Emails to send", copy=True)
    x_invoice = fields.Many2one("account.move", string="Invoice Ref", copy=False)
    x_invoice_generated = fields.<PERSON><PERSON>an(string="Invoice Generated?", copy=False)
    x_monthly_recurrent = fields.<PERSON><PERSON>an(string="Monthly Recurrent?", copy=True)
    x_previous_task = fields.Many2one("project.task", string="Previous Task", copy=True)
    x_task_template = fields.Many2one("x_task_template", string="Task Template", copy=True)
    x_user_assigned = fields.Many2many("res.users", string="Assigned To", copy=True)


class TaskTemplate(models.Model):
    _name = "x_task_template"
    _description = "Task Template"

    x_name = fields.Char(string="Name", required=True)
    x_description = fields.Html(string="Description")