<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Server Action: Force Transfer Stick Count from Sale to Invoice -->
        <record id="server_action_force_transfer_stick_count" model="ir.actions.server">
            <field name="name">Force Transfer Stick Count from Sale</field>
            <field name="model_id" ref="account.model_account_move_line"/>
            <field name="state">code</field>
            <field name="code">
# Force transfer stick count from sale order lines to invoice lines
transferred = records.force_transfer_stick_count_from_sale()

if transferred:
    message = "Transferred stick counts:\n" + "\n".join(transferred)
else:
    message = "No transfers needed or no sale order lines found"

raise UserError(message)
            </field>
        </record>

        <!-- Server Action: Debug Invoice Line Stick Count -->
        <record id="server_action_debug_invoice_stick_count" model="ir.actions.server">
            <field name="name">Debug Invoice Line Stick Count</field>
            <field name="model_id" ref="account.model_account_move_line"/>
            <field name="state">code</field>
            <field name="code">
# Debug invoice line stick count
debug_info = []

for line in records:
    debug_info.append(f"=== Invoice Line {line.id} ===")
    debug_info.append(f"Product: {line.product_id.name if line.product_id else 'None'}")
    debug_info.append(f"Quantity: {line.quantity}")
    
    # Check if has stick count field
    has_field = hasattr(line, 'x_cigar_stick_count')
    debug_info.append(f"Has stick count field: {has_field}")
    
    if has_field:
        current_count = getattr(line, 'x_cigar_stick_count', 0)
        debug_info.append(f"Current stick count: {current_count}")
    
    # Check sale order lines
    if line.sale_line_ids:
        debug_info.append(f"Sale lines: {len(line.sale_line_ids)}")
        for sale_line in line.sale_line_ids:
            sale_has_field = hasattr(sale_line, 'x_cigar_stick_count')
            debug_info.append(f"  Sale line {sale_line.id}: has_field={sale_has_field}")
            if sale_has_field:
                sale_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                debug_info.append(f"  Sale line stick count: {sale_count}")
    else:
        debug_info.append("No sale order lines linked")
    
    debug_info.append("")

message = "\n".join(debug_info)
raise UserError(message)
            </field>
        </record>
    </data>
</odoo>
