# NewJKS Module - Migration from Odoo 13 to Odoo 18 CE

## Overview
This document outlines the changes made to migrate the NewJKS project task extensions module from Odoo 13 to Odoo 18 CE.

## Issues Fixed

### 1. Module Import Structure
**Problem:** Field "x_user_assigned" does not exist in model "project.task"
**Root Cause:** Incorrect import structure in `__init__.py` files

**Fixed:**
- `__init__.py`: Added `from . import models`
- `models/__init__.py`: Changed `from . import models` to `from . import task`

### 2. Deprecated `attrs` Attribute
**Problem:** `attrs` attribute deprecated since Odoo 17.0
**Fixed:**
- `views/task_views.xml` line 23: 
  - **Before:** `attrs="{'invisible': [('x_monthly_recurrent', '=', False)]}"`
  - **After:** `invisible="not x_monthly_recurrent"`

### 3. Automated Actions Compatibility Issues
**Fixed in `data/automated_actions.xml`:**

#### A. Account Move Type Field Change
- **Before:** `('move_id.type','=','out_invoice')`
- **After:** `('move_id.move_type','=','out_invoice')`
- **Reason:** Field renamed in Odoo 14+

#### B. Exception Handling Updates
- **Before:** `raise Warning("message")`
- **After:** `raise UserError("message")`
- **Reason:** `Warning` deprecated, use `UserError` from `odoo.exceptions`

#### C. Automated Actions Complete Restructure
**Problem:** In Odoo 18, automated actions use a completely different structure
- **Before:** Single `ir.actions.server` records with `binding_type` field
- **After:** Separate `ir.actions.server` + `base.automation` records

**Changes Made:**
- Split each automated action into two records:
  1. **Server Action** (`ir.actions.server`): Contains the actual code to execute
  2. **Automation Rule** (`base.automation`): Defines when to trigger the server action
- Updated all 7 automated actions:
  - x_assign Description from Task Template
  - x_Send Emails from Task
  - x_Get Default Pricing
  - x_check emails for task
  - x_set approved by in Task
  - x_set Timesheets Automatically
  - x_Timesheet Days Calculation

### 4. Manifest File Updates
**Updated `__manifest__.py`:**
- Version: `1.0` → `********.0`
- Added proper name: `"JKS Project Task Extensions"`
- Added category: `"Project"`
- Added `auto_install: False`
- Improved summary and description

## Custom Fields Added
The module extends `project.task` with the following custom fields:
- `x_approved_by`: Many2one to res.users
- `x_billing_amt`: Float for billing amount
- `x_date_of_month`: Integer for monthly recurrence
- `x_emails`: Char field for email addresses
- `x_invoice`: Many2one to account.move
- `x_invoice_generated`: Boolean flag
- `x_monthly_recurrent`: Boolean for recurring tasks
- `x_previous_task`: Many2one to project.task
- `x_task_template`: Many2one to x_task_template
- `x_user_assigned`: Many2many to res.users

## Custom Model Added
- `x_task_template`: Task template model with name and description fields

## Automated Actions
The module includes several automated actions:
1. Assign description from task template
2. Send emails from task
3. Get default pricing
4. Check emails for task
5. Set approved by in task
6. Set timesheets automatically
7. Timesheet days calculation

## Mail Templates
Includes 10 mail templates for various business processes:
1. Invoice: Send by email
2. Task: Assign
3. Task: Rating Request
4. Task: Reception Acknowledgment
5. WhatsApp Sale template
6. WhatsApp Contact template
7. WhatsApp Inventory template
8. WhatsApp Invoice template
9. WhatsApp Purchase template
10. WhatsApp Generic template

## Migration Status: COMPLETE

All necessary changes have been made to ensure compatibility with Odoo 18 CE:
- ✅ Fixed module import structure
- ✅ Updated deprecated `attrs` syntax
- ✅ Fixed automated actions compatibility
- ✅ Updated manifest for Odoo 18
- ✅ Maintained all custom functionality

## Version Information
- **Original Version:** Odoo 13
- **Target Version:** Odoo 18 CE
- **Module Version:** ********.0
- **Migration Date:** 2025-01-22
