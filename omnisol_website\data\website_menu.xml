<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Website Menu Structure -->
        <record id="omnisol_main_menu" model="website.menu">
            <field name="name">Main Menu</field>
            <field name="website_id" ref="omnisol_website"/>
            <field name="parent_id" eval="False"/>
            <field name="sequence">1</field>
        </record>

        <!-- Home Menu -->
        <record id="omnisol_menu_home" model="website.menu">
            <field name="name">Home</field>
            <field name="url">/</field>
            <field name="page_id" ref="omnisol_homepage"/>
            <field name="parent_id" ref="omnisol_main_menu"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">10</field>
        </record>

        <!-- Products Menu -->
        <record id="omnisol_menu_products" model="website.menu">
            <field name="name">Products</field>
            <field name="url">/products</field>
            <field name="page_id" ref="omnisol_products_page"/>
            <field name="parent_id" ref="omnisol_main_menu"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">20</field>
        </record>

        <!-- About Menu -->
        <record id="omnisol_menu_about" model="website.menu">
            <field name="name">About</field>
            <field name="url">/about</field>
            <field name="page_id" ref="omnisol_about_page"/>
            <field name="parent_id" ref="omnisol_main_menu"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">30</field>
        </record>

        <!-- Contact Menu -->
        <record id="omnisol_menu_contact" model="website.menu">
            <field name="name">Contact</field>
            <field name="url">/contact</field>
            <field name="page_id" ref="omnisol_contact_page"/>
            <field name="parent_id" ref="omnisol_main_menu"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">40</field>
        </record>

        <!-- Footer Menu Structure -->
        <record id="omnisol_footer_menu" model="website.menu">
            <field name="name">Footer Menu</field>
            <field name="website_id" ref="omnisol_website"/>
            <field name="parent_id" eval="False"/>
            <field name="sequence">100</field>
        </record>

        <!-- Footer - Quick Links -->
        <record id="omnisol_footer_quicklinks" model="website.menu">
            <field name="name">Quick Links</field>
            <field name="parent_id" ref="omnisol_footer_menu"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">10</field>
        </record>

        <record id="omnisol_footer_home" model="website.menu">
            <field name="name">Home</field>
            <field name="url">/</field>
            <field name="parent_id" ref="omnisol_footer_quicklinks"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">10</field>
        </record>

        <record id="omnisol_footer_products" model="website.menu">
            <field name="name">Products</field>
            <field name="url">/products</field>
            <field name="parent_id" ref="omnisol_footer_quicklinks"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">20</field>
        </record>

        <record id="omnisol_footer_about" model="website.menu">
            <field name="name">About</field>
            <field name="url">/about</field>
            <field name="parent_id" ref="omnisol_footer_quicklinks"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">30</field>
        </record>

        <record id="omnisol_footer_contact" model="website.menu">
            <field name="name">Contact</field>
            <field name="url">/contact</field>
            <field name="parent_id" ref="omnisol_footer_quicklinks"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">40</field>
        </record>

        <!-- Footer - Product Categories -->
        <record id="omnisol_footer_product_categories" model="website.menu">
            <field name="name">Product Categories</field>
            <field name="parent_id" ref="omnisol_footer_menu"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">20</field>
        </record>

        <record id="omnisol_footer_laser_cutters" model="website.menu">
            <field name="name">Laser Cutters</field>
            <field name="url">/products#laser-cutters</field>
            <field name="parent_id" ref="omnisol_footer_product_categories"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">10</field>
        </record>

        <record id="omnisol_footer_engravers" model="website.menu">
            <field name="name">Engraving Machines</field>
            <field name="url">/products#engravers</field>
            <field name="parent_id" ref="omnisol_footer_product_categories"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">20</field>
        </record>

        <record id="omnisol_footer_industrial" model="website.menu">
            <field name="name">Industrial Equipment</field>
            <field name="url">/products#industrial</field>
            <field name="parent_id" ref="omnisol_footer_product_categories"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">30</field>
        </record>

        <record id="omnisol_footer_accessories" model="website.menu">
            <field name="name">Accessories</field>
            <field name="url">/products#accessories</field>
            <field name="parent_id" ref="omnisol_footer_product_categories"/>
            <field name="website_id" ref="omnisol_website"/>
            <field name="sequence">40</field>
        </record>
    </data>
</odoo>
