<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Custom field for cigars per box on product template -->
        <record id="field_product_template_x_cigars_per_box" model="ir.model.fields">
            <field name="name">x_cigars_per_box</field>
            <field name="field_description">Cigars per Box</field>
            <field name="model_id" ref="product.model_product_template"/>
            <field name="ttype">integer</field>
            <field name="store" eval="True"/>
            <field name="index" eval="True"/>
            <field name="help">Number of individual cigars in this box/pack. Used for automatic stick count calculation.</field>
        </record>

        <!-- Custom field for cigars per box on product product -->
        <record id="field_product_product_x_cigars_per_box" model="ir.model.fields">
            <field name="name">x_cigars_per_box</field>
            <field name="field_description">Cigars per Box</field>
            <field name="model_id" ref="product.model_product_product"/>
            <field name="ttype">integer</field>
            <field name="store" eval="True"/>
            <field name="index" eval="True"/>
            <field name="help">Number of individual cigars in this box/pack. Used for automatic stick count calculation.</field>
        </record>

        <!-- Custom field for sticks per unit on product template (alternative field name) -->
        <record id="field_product_template_x_sticks_per_unit" model="ir.model.fields">
            <field name="name">x_sticks_per_unit</field>
            <field name="field_description">Sticks per Unit</field>
            <field name="model_id" ref="product.model_product_template"/>
            <field name="ttype">integer</field>
            <field name="store" eval="True"/>
            <field name="index" eval="True"/>
            <field name="help">Number of individual sticks/cigars per unit. Used for automatic stick count calculation.</field>
        </record>

        <!-- Custom field for sticks per unit on product product (alternative field name) -->
        <record id="field_product_product_x_sticks_per_unit" model="ir.model.fields">
            <field name="name">x_sticks_per_unit</field>
            <field name="field_description">Sticks per Unit</field>
            <field name="model_id" ref="product.model_product_product"/>
            <field name="ttype">integer</field>
            <field name="store" eval="True"/>
            <field name="index" eval="True"/>
            <field name="help">Number of individual sticks/cigars per unit. Used for automatic stick count calculation.</field>
        </record>
    </data>
</odoo>
