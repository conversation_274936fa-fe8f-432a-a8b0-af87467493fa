<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <record id="view_project_task_form_inherit_jks" model="ir.ui.view">
    <field name="name">project.task.form.inherit.jks</field>
    <field name="model">project.task</field>
    <field name="inherit_id" ref="project.view_task_form2"/>
    <field name="arch" type="xml">

      <xpath expr="//sheet/group" position="after">
        <group string="JKS Custom Fields">
          <group>
            <field name="x_user_assigned" widget="many2many_tags"/>
            <field name="x_previous_task"/>
            <field name="x_task_template"/>
            <field name="x_invoice_generated" invisible="1"/>
            <field name="x_emails" readonly="1"/>
          </group>
          <group>
            <field name="x_billing_amt" groups="account.group_account_invoice"/>
            <field name="x_invoice" groups="account.group_account_invoice"/>
            <field name="x_approved_by" groups="account.group_account_invoice"/>
            <field name="x_monthly_recurrent"/>
            <field name="x_date_of_month" invisible="not x_monthly_recurrent"/>
          </group>
        </group>
      </xpath>

    </field>
  </record>
  <record id="view_x_task_template_tree" model="ir.ui.view">
        <field name="name">x.task.template.tree</field>
        <field name="model">x_task_template</field>
        <field name="arch" type="xml">
            <list string="Task Templates">
                <field name="x_name"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_x_task_template_form" model="ir.ui.view">
        <field name="name">x.task.template.form</field>
        <field name="model">x_task_template</field>
        <field name="arch" type="xml">
            <form string="Task Template">
                <sheet>
                    <group>
                        <group>
                            <field name="x_name"/>
                            <field name="x_description"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_x_task_template" model="ir.actions.act_window">
        <field name="name">Task Templates</field>
        <field name="res_model">x_task_template</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- Menu item -->
    <menuitem id="menu_x_task_template_root" name="Task Templates" sequence="10"/>
    <menuitem id="menu_x_task_template" name="Manage Task Templates"
              parent="menu_x_task_template_root"
              action="action_x_task_template"/>
</odoo>
