# Odoo WhatsApp Integration - Migration from Odoo 13 to Odoo 18 CE

## Overview
This document outlines the changes made to migrate the WhatsApp Integration module from Odoo 13 to Odoo 18 CE.

## Changes Made

### 1. Manifest File Updates (`__manifest__.py`)
- Updated version from `0.1` to `********.0`
- Updated category reference from Odoo 13 to Odoo 18 documentation
- Changed category from `'Whatsapp'` to `'Extra Tools'` (more standard)
- Added `'license': 'LGPL-3'` field
- Added `'mail'` dependency for email template functionality
- Added proper description with features list
- Added `'installable': True`, `'auto_install': False`, `'application': False` fields

### 2. Model Files Updates
Updated all model files to remove deprecated `view_type` attribute from action dictionaries:

#### Files Updated:
- `models/sale_fun.py`
- `models/inventory_fun.py` 
- `models/purchase_fun.py`
- `models/invoice_fun.py`
- `models/contacts_fun.py`

#### Changes:
- Removed `'view_type': 'form'` from all `ir.actions.act_window` dictionaries
- This attribute was deprecated in Odoo 11+ and removed in later versions

### 3. Wizard Files Updates
- Updated `wizard/share_action.py` to use `urllib.parse.quote` instead of deprecated `urllib.quote`
- Changed import from `import urllib.parse as urllib` to `import urllib.parse as parse`
- Updated function call from `urllib.quote()` to `parse.quote()`
- **Fixed html2text dependency issue**:
  - Replaced `import html2text` with `from odoo.tools import html2plaintext` in `message_wizard.py`
  - Replaced `html2text.html2text()` with `html2plaintext()` (Odoo's built-in function)
  - Removed unnecessary `html2text` import and call from `message_wizard_multiple_contact.py`

### 4. View Files
- **Updated deprecated `attrs` attribute usage** (deprecated since Odoo 17.0):
  - Replaced `attrs="{'invisible': [('mobile', '=', False)]}"` with `invisible="not mobile"` in `views/views.xml`
  - Replaced all `attrs` usage in `wizard/share_action.xml` with individual `invisible`, `required` attributes:
    - `attrs="{'invisible': ['|',('access_warning', '!=', ''),('share_type', '=', 'whatsapp')]}"` → `invisible="access_warning != '' or share_type == 'whatsapp'"`
    - `attrs="{'invisible': [('share_type', '=', 'mail')]}"` → `invisible="share_type == 'mail'"`
    - `attrs="{'required': [('share_type','==','whatsapp')]}"` → `required="share_type == 'whatsapp'"`
    - And similar conversions for other fields
- **Fixed settings view inheritance** in `views/setting_inherit_view.xml`:
  - Updated XPath expression to be compatible with Odoo 18 settings structure
  - Changed from specific integration section targeting to generic form insertion
  - Simplified settings block structure for better compatibility

### 5. Security Files
- Access rights file (`security/ir.model.access.csv`) - No changes required
- Security groups file (`security/sms_security.xml`) - No changes required

### 6. Email Templates
- All email templates in `views/template.xml` use standard QWeb syntax
- No changes required as syntax is still supported in Odoo 18

## Compatibility Notes

### Python 3 Compatibility
- Fixed deprecated `urllib.quote` usage
- **Removed external html2text dependency** - replaced with Odoo's built-in `html2plaintext`
- All other Python code is already Python 3 compatible

### Odoo 18 API Compatibility
- Removed deprecated `view_type` attributes
- **Replaced deprecated `attrs` attributes** with new Odoo 17+ syntax (`invisible`, `readonly`, `required`)
- All ORM methods used are still supported in Odoo 18
- Field definitions are compatible with Odoo 18

### Dependencies
- All module dependencies (`base`, `sale`, `web`, `stock`, `purchase`, `account`, `contacts`, `mail`) are available in Odoo 18 CE

## Testing Recommendations

1. **Installation Test**: Install the module in a clean Odoo 18 CE instance
2. **Functionality Tests**:
   - Test WhatsApp message sending from Sales Orders
   - Test WhatsApp message sending from Purchase Orders
   - Test WhatsApp message sending from Invoices
   - Test WhatsApp message sending from Stock Pickings
   - Test WhatsApp message sending from Contacts
   - Test multiple contact messaging
   - Test template-based messaging
   - Test direct messaging functionality

3. **UI Tests**:
   - Verify all buttons appear correctly in forms
   - Test wizard forms open and function properly
   - Verify settings page integration works

4. **Security Tests**:
   - Test access rights for different user groups
   - Verify direct message functionality is properly restricted

## Migration Status: COMPLETE

All necessary changes have been made to ensure compatibility with Odoo 18 CE. The module should now function properly in Odoo 18 environment.

## Version Information
- **Original Version**: Odoo 13
- **Target Version**: Odoo 18 CE
- **Module Version**: ********.0
- **Migration Date**: 2025-01-22
