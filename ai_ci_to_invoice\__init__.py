# -*- coding: utf-8 -*-
"""
Product Cigarette & Tobacco Classification Module

This Odoo module provides category-based classification and sorting functionality
for tobacco-related products including cigars, tobacco products, and disposable items.

Key Features:
- Category-based product classification (no custom fields needed)
- Automatic classification based on product category hierarchy
- Sorting functionality for sales orders and invoices by product type
- PDF reports with products grouped and sorted by type
- Server actions for easy sorting via Action menu

Note: Cigarette classification is planned but not yet implemented in this version.

The module extends core Odoo models (product.template, product.product,
sale.order, sale.order.line, account.move, account.move.line) to add
classification methods and sorting capabilities while preserving existing
customizations and functionality.

Author: Arihantai
Website: https://www.arihantai.com/
Version: 13.0.1.0.4
"""

# Import models package to register all model extensions
from . import models
