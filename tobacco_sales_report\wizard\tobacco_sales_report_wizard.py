# -*- coding: utf-8 -*-
"""
Tobacco Sales Report Wizard

This wizard provides a user interface for generating tobacco sales Excel reports
with date range selection and download functionality.
"""

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class TobaccoSalesReportWizard(models.TransientModel):
    """
    Wizard for generating tobacco sales reports.
    
    This wizard allows users to select a date range and generate an Excel report
    containing detailed tobacco and cigar sales information.
    """
    _name = 'tobacco.sales.report.wizard'
    _description = 'Tobacco Sales Report Wizard'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=lambda _: fields.Date.today().replace(day=1),
        help="Start date for the report period"
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.today,
        help="End date for the report period"
    )
    partner_ids = fields.Many2many(
        'res.partner',
        string='Customers',
        help="Select specific customers for the report. Leave empty to include all customers."
    )
    product_type = fields.Selection([
        ('both', 'Both Tobacco and Cigars'),
        ('tobacco', 'Tobacco Only'),
        ('cigars', 'Cigars Only'),
    ], string='Product Type', default='both', required=True,
       help="Select which product types to include in the report")
    user_id = fields.Many2one(
        'res.users',
        string='Report User',
        required=True,
        default=lambda self: self._get_default_user(),
        help="Select user to filter invoices. Official reports <NAME_EMAIL> to exclude test data."
    )

    def _get_default_user(self):
        """Get the default user for reports (official user)."""
        official_user = self.env['res.users'].search([('login', '=', '<EMAIL>')], limit=1)
        return official_user.id if official_user else False

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        """Validate that from date is not after to date."""
        for record in self:
            if record.date_from > record.date_to:
                raise UserError(_('From Date cannot be after To Date.'))

    def action_generate_report(self):
        """
        Generate the tobacco sales Excel report.
        
        Creates a tobacco.sales.report record with the selected parameters
        and triggers the report generation.
        
        Returns:
            dict: Action to open the generated report or download it
        """
        # Create the report record
        report = self.env['tobacco.sales.report'].create({
            'date_from': self.date_from,
            'date_to': self.date_to,
            'partner_ids': [(6, 0, self.partner_ids.ids)],
            'product_type': self.product_type,
            'user_id': self.user_id.id,
        })
        
        # Generate the report
        action = report.generate_report()
        
        return action

    def action_preview_data(self):
        """
        Preview the data that will be included in the report.
        
        Returns:
            dict: Action to open a tree view with the sales data
        """
        # Get the sales data without generating Excel
        report = self.env['tobacco.sales.report'].create({
            'date_from': self.date_from,
            'date_to': self.date_to,
            'partner_ids': [(6, 0, self.partner_ids.ids)],
            'product_type': self.product_type,
            'user_id': self.user_id.id,
        })
        
        sales_data = report._collect_sales_data()
        
        if not sales_data:
            raise UserError(_('No tobacco or cigar sales found for the selected period.'))
        
        # Create a message with summary information
        message = _(
            "Found %d invoices with tobacco/cigar sales in the selected period.\n\n"
            "Date Range: %s to %s\n"
            "Total Records: %d"
        ) % (
            len(sales_data),
            self.date_from.strftime('%m/%d/%Y'),
            self.date_to.strftime('%m/%d/%Y'),
            len(sales_data)
        )
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Report Preview'),
                'message': message,
                'type': 'info',
                'sticky': False,
            }
        }
