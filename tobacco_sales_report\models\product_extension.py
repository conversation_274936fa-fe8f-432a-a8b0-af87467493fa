# -*- coding: utf-8 -*-
"""
Product Extension for Tobacco Sales Reporting

This module extends product.template and product.product to add custom fields
for cigar stick count calculation. These fields are only relevant for cigar
category products and help with automatic stick count calculation.
"""

from odoo import models, fields


class ProductTemplate(models.Model):
    """
    Extends product.template to add cigar-specific fields for stick count calculation.
    """
    _inherit = 'product.template'

    x_cigars_per_box = fields.Integer(
        string='Cigars per Box',
        help='Number of individual cigars in this box/pack. Used for automatic stick count calculation.',
        default=0
    )
    
    x_sticks_per_unit = fields.Integer(
        string='Sticks per Unit',
        help='Number of individual sticks/cigars per unit. Used for automatic stick count calculation.',
        default=0
    )


class ProductProduct(models.Model):
    """
    Extends product.product to add cigar-specific fields for stick count calculation.
    """
    _inherit = 'product.product'

    x_cigars_per_box = fields.Integer(
        string='Cigars per Box',
        help='Number of individual cigars in this box/pack. Used for automatic stick count calculation.',
        default=0
    )
    
    x_sticks_per_unit = fields.Integer(
        string='Sticks per Unit',
        help='Number of individual sticks/cigars per unit. Used for automatic stick count calculation.',
        default=0
    )
