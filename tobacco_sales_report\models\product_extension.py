# -*- coding: utf-8 -*-
"""
Product Extension for Tobacco Sales Reporting

This module extends product.template and product.product to provide helper methods
for cigar stick count calculation. The custom fields (x_cigars_per_box, x_sticks_per_unit)
must be created via Odoo Studio.

NO FIELD DEFINITIONS - Fields must be created via Studio only!
"""

from odoo import models


class ProductTemplate(models.Model):
    """
    Extends product.template to provide helper methods for cigar stick count calculation.

    This extension only provides helper methods and does NOT define any fields.
    The x_cigars_per_box and x_sticks_per_unit fields must be created via Odoo Studio.
    """
    _inherit = 'product.template'


class ProductProduct(models.Model):
    """
    Extends product.product to provide helper methods for cigar stick count calculation.

    This extension only provides helper methods and does NOT define any fields.
    The x_cigars_per_box and x_sticks_per_unit fields must be created via Odoo Studio.
    """
    _inherit = 'product.product'
