<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 1. Invoice: Send by email -->
        <record id="mail_template_invoice_send" model="mail.template">
            <field name="name">Invoice: Send by email</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">${object.company_id.name} Invoice (Ref ${object.name or 'n/a'})</field>
            <field name="email_from"><EMAIL></field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="report_name">Invoice_${(object.name or '').replace('/','_')}${object.state == 'draft' and '_draft' or ''}</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-size:13px;font-family:'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;">
    <p>
        Dear
        % if object.partner_id.parent_id:
            ${object.partner_id.name} (${object.partner_id.parent_id.name}),
        % else:
            ${object.partner_id.name},
        % endif
        <br><br>
        Here is your
        % if object.name:
            invoice <strong>${object.name}</strong>
        % else:
            invoice
        %endif
        % if object.invoice_origin:
            (with reference: ${object.invoice_origin})
        % endif
        amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>
        from ${object.company_id.name}.
        % if object.invoice_payment_state == 'paid':
            This invoice is already paid.
        % else:
            Please remit payment at your earliest convenience.
        % endif
        <br><br>         
    </p>
    <p><b>Please also find MSME registration certificate of our firm having UDYAM Registration Number - UDYAM-GJ-01-0020659. Please make the payment at your earliest convenience.</b></p>
    <p>Bank Name: ICICI Bank</p>
    <p>Account Number : ************</p>
    <p>Account Holder's name : JAIN KEDIA AND SHARMA</p>
    <p>Contact : **********</p>
    <p>Ifsc Code : ICIC0001655&nbsp;</p>
    <p>Email Id : <EMAIL>&nbsp;</p>
    <p>
        Do not hesitate to contact us if you have any questions.<br>
        ... Please do not reply to this E-Mail. Reply to the following mails for any query
        <br><br>
        <a href="mailto:<EMAIL>"><EMAIL></a>
        <br><br>
        <a href="mailto:<EMAIL>"><EMAIL></a>
        <br><br>
        Feel free to contact us on
        <br><br>
        Tarak: **********
        <br><br>
        Ashish: **********
    </p>
</div>
                ]]>
            </field>
        </record>

        <!-- 2. Task: Assign -->
        <record id="mail_template_task_assign" model="mail.template">
            <field name="name">Task: Assign</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="subject">${object.project_id.name}: ${object.name}</field>
            <field name="email_from">${(object.rating_get_rated_partner_id().email_formatted if object.rating_get_rated_partner_id() else user.email_formatted) | safe}</field>
            <field name="email_to">${object.x_emails}</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-size:13px;font-family:'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;">
    % set url = 'http://erp.jainkediasharma.com:8069/web?#id='+str(object.id)+'&action=178&model=project.task&view_type=form&cids=1&menu_id=114'
    <br><br>
    You have been assigned to ${object.name} under ${object.project_id.name} project by ${object.user_id.name}. Deadline for the task is ${object.date_deadline}.
    <br><br>
    <table style="display:inline-table;vertical-align:middle"><tbody><tr><td height="30" style="text-align:left; margin:4.5px 9px; border-radius:0px; background-color:#17a2b8;">
        <a href="${url}" style="border-radius:0px;padding:0.375rem 0.75rem;cursor:pointer;line-height:1.5;font-size:1.08333rem;background-color:#17a2b8;color:#fff;text-decoration:none;display:inline-block;">
            View Task
        </a>
    </td></tr></tbody></table>
</div>
                ]]>
            </field>
        </record>

        <!-- 3. Task: Rating Request -->
        <record id="mail_template_task_rating_request" model="mail.template">
            <field name="name">Task: Rating Request</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="subject">${object.project_id.company_id.name}: Satisfaction Survey</field>
            <field name="email_from">${(object.rating_get_rated_partner_id().email_formatted if object.rating_get_rated_partner_id() else user.email_formatted) | safe}</field>
            <field name="partner_to">${object.rating_get_partner_id().id}</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-size:13px;font-family:'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;">
    % set access_token = object.rating_get_access_token()
    % set partner = object.rating_get_partner_id()
    <table border="0" cellpadding="0" cellspacing="0" width="590" style="width:100%;margin:0px auto;">
        <tbody>
            <tr><td>
                % if partner.name:
                    Hello ${partner.name},<br><br>
                % else:
                    Hello,<br><br>
                % endif
                Please take a moment to rate our services related to the task "<strong>${object.name}</strong>"
                % if object.rating_get_rated_partner_id().name:
                    assigned to <strong>${object.rating_get_rated_partner_id().name}</strong>.<br>
                % else:
                    .<br>
                % endif
            </td></tr>
            <tr><td style="text-align:center;">
                <strong>Tell us how you feel about our service</strong><br>
                <span style="color:#888888">(click on one of these smileys)</span>
                <table style="width:100%;text-align:center;margin-top:32px;margin-bottom:32px;">
                    <tr>
                        <td>
                            <a href="/rating/${access_token}/10"><img alt="Satisfied" src="/rating/static/src/img/rating_10.png" title="Satisfied" style="border:none;vertical-align:middle;"/></a>
                        </td>
                        <td>
                            <a href="/rating/${access_token}/5"><img alt="Not satisfied" src="/rating/static/src/img/rating_5.png" title="Not satisfied" style="border:none;vertical-align:middle;"/></a>
                        </td>
                        <td>
                            <a href="/rating/${access_token}/1"><img alt="Highly Dissatisfied" src="/rating/static/src/img/rating_1.png" title="Highly Dissatisfied" style="border:none;vertical-align:middle;"/></a>
                        </td>
                    </tr>
                </table>
                We appreciate your feedback. It helps us to improve continuously.
                % if object.project_id.rating_status == 'stage':
                    <br><span style="font-size:12px; opacity:0.5;">This customer survey has been sent because your task has been moved to the stage <b>${object.stage_id.name}</b></span>
                % endif
                % if object.project_id.rating_status == 'periodic':
                    <br><span style="font-size:12px; opacity:0.5;">This customer survey is sent <b>${object.project_id.rating_status_period}</b> as long as the task is in the <b>${object.stage_id.name}</b> stage.</span>
                % endif
            </td></tr>
        </tbody>
    </table>
</div>
                ]]>
            </field>
        </record>

        <!-- 4. Task: Reception Acknowledgment -->
        <record id="mail_template_task_reception_ack" model="mail.template">
            <field name="name">Task: Reception Acknowledgment</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="subject">Reception of ${object.name}</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-size:13px;font-family:'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;">
    Dear ${object.partner_id.name or 'customer'},<br>
    Thank you for your enquiry.<br>
    If you have any questions, please let us know.
    <br><br>
    Thank you,<br>
</div>
                ]]>
            </field>
        </record>

        <!-- 5. WhatsApp Sale template -->
        <record id="mail_template_whatsapp_sale" model="mail.template">
            <field name="name">WhatsApp Sale template</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="subject">Sales template</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-family:'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif;font-size:12px;">
    <div>Hello *${object.partner_id.name or ''}*,</div>
    % if object.state == 'draft' or object.state == 'sent':
        Your Quotation *${object.name}* with amount *${format_amount(object.amount_total, object.currency_id)}* is ready.
        <div>Your quotation date and time is "${object.date_order}"</div>
        <div>
            Quotation details are as follows:<br>
            % for each in object:
                % for id in each.order_line:
                    *Product: ${id.product_id.name}* <br>
                    *Qty: ${id.product_uom_qty}*<br>
                    *Price: ${id.price_subtotal}*<br>
                % endfor
            % endfor
        </div>
    % else:
        <div>Your Sale Order Number *${object.name}* with amount *${format_amount(object.amount_total, object.currency_id)}* is Confirmed.</div>
        <div>Your order date and time is "${object.date_order}"</div>
        <div>
            Your order details are as follows:<br>
            % for each in object:
                % for id in each.order_line:
                    *Product: ${id.product_id.name}* <br>
                    *Qty: ${id.product_uom_qty}*<br>
                    *Price: ${id.price_subtotal}*<br>
                % endfor
            % endfor
        </div>
    % endif
    <div>If you have any questions, please feel free to contact us.</div>
</div>
                ]]>
            </field>
        </record>

        <!-- 6. Whats app Contact template -->
        <record id="mail_template_whatsapp_contact" model="mail.template">
            <field name="name">WhatsApp Contact Template</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="subject">Contact Message Template</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-family:'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif;font-size:12px;">
    Hello *${object.name}*,
    <br><br>
    Thank you for contacting us.
    <br><br>
    If you have any queries or requests, please reply to this message.
</div>
                ]]>
            </field>
        </record>

        <!-- 7. Whats app Inventory template -->
        <record id="mail_template_whatsapp_inventory" model="mail.template">
            <field name="name">WhatsApp Inventory Template</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="subject">Inventory Template</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-family:'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif;font-size:12px;">
    <p>
    <div>
        Hello *${object.partner_id.name or ''}*,
    </div>
    <div>
        % if object.state == 'draft':
            We have received your order *${object.name}*. It will be shipped soon
            <div>
                Order details are as follows: <br>
                % for each in object:
                    % for id in each.move_ids_without_package:
                        *Product: ${id.product_id.name}* <br>
                        *Qty: ${id.product_uom_qty}* <br>
                    % endfor
                % endfor
            </div>
        % endif
        % if object.state == 'confirmed':
            Your order *${object.name}* is ready. It will be shipped soon
            <div>
                Order details are as follows: <br>
                % for each in object:
                    % for id in each.move_line_ids_without_package:
                        *Product: ${id.product_id.name}* <br>
                        *Qty: ${id.product_uom_qty}* <br>
                    % endfor
                % endfor
            </div>
        % endif
        % if object.state == 'assigned':
            Your order *${object.name}* is ready. It will be shipped soon
            <div>
                Order details are as follows: <br>
                % for each in object:
                    % for id in each.move_line_ids_without_package:
                        *Product: ${id.product_id.name}* <br>
                        *Qty: ${id.product_uom_qty}* <br>
                    % endfor
                % endfor
            </div>
        % endif
        % if object.state == 'done':
            Your order *${object.name}* is shipped.
            <div>
                Order details are as follows: <br>
                % for each in object:
                    % for id in each.move_line_ids_without_package:
                        *Product: ${id.product_id.name}* <br>
                        *Qty: ${id.qty_done}* <br>
                    % endfor
                % endfor
            </div>
        % endif
    </div>
    <div>
        If you have any questions, please feel free to contact us.
    </div>
    </p>
</div>
                ]]>
            </field>
        </record>

        <!-- 8. Whats app Invoice template -->
        <record id="mail_template_whatsapp_invoice" model="mail.template">
            <field name="name">WhatsApp Invoice Template</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">Invoice Template</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-size:13px;font-family:'Lucida Grande', Helvetica, Verdana, Arial, sans-serif;">
    <p>
        Dear *${object.partner_id.name}*,
    </p>
    <div>Here is your invoice *${object.name}* with amount *${format_amount(object.amount_total, object.currency_id)}* from ${object.company_id.name}.</div>
    <div>
        % if object.invoice_payment_state == 'paid':
            This invoice is already paid. Amount due is *${format_amount(object.amount_residual,object.currency_id)}*
        % else:
            Please remit payment at your earliest convenience. Amount due is *${format_amount(object.amount_residual,object.currency_id)}*
        % endif
    </div>
    <span style="font-size:12px;">The invoice date and time is "${object.invoice_date}"</span>
    <div>Your order details are as follows:<br>
        % for each in object:
            % for id in each.invoice_line_ids:
                *Product: ${id.product_id.name}*<br>
                *Qty: ${id.quantity}*<br>
                *Price: ${id.price_unit}*<br>
            % endfor
        % endfor
    </div>
    <div>
        <span>Our Bank Details are as follows:&nbsp;&nbsp;</span>
    </div>
    <div><span>Bank Name: ICICI Bank</span></div>
    <div><span>Account Number : ************</span></div>
    <div><span>Account Holder's name : JAIN KEDIA AND SHARMA</span></div>
    <div><span>Contact : **********</span></div>
    <div><span>IFSC Code : ICIC0001655</span></div>
    <div>
        <span>Email Id : <EMAIL>&nbsp;</span>
    </div>
    <div>
        The Invoice is already mailed to your *E-mail Id: ${object.partner_id.email}*. Kindly check your E-Mail for Document copy of the Invoice.
    </div>
    <div>
        <span>If you have any questions, please feel free to contact us.</span><br>
        Tarak: **********<br>
        Ashish: **********<br>
    </div>
</div>
                ]]>
            </field>
        </record>

        <!-- 9. Whats app Purchase template -->
        <record id="mail_template_whatsapp_purchase" model="mail.template">
            <field name="name">WhatsApp Purchase Template</field>
            <field name="model_id" ref="purchase.model_purchase_order"/>
            <field name="subject">Purchase Template</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-family:'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif;font-size:12px;">
    <p>
    <div>
        Hello *${object.partner_id.name or ''}*,
    </div>
    % if object.state == 'draft' or object.state == 'sent':
        This is about Request For Quotation (RFQ) *${object.name}* with amount *${format_amount(object.amount_total, object.currency_id)}*
        <div>
            Quotation details are as follows: <br>
            % for each in object:
                % for id in each.order_line:
                    *Product: ${id.product_id.name}* <br>
                    *Qty: ${id.product_qty}* <br>
                    *Price: ${id.price_subtotal}*<br>
                % endfor
            % endfor
        </div>
    % else:
        <div>
            Your Purchase Order Number *${object.name}* with amount *${format_amount(object.amount_total, object.currency_id)}* is Confirmed.
        </div>
        <div>
            Order confirmation date and time is "${object.date_approve}"
        </div>
        <div>
            Order details are as follows: <br>
            % for each in object:
                % for id in each.order_line:
                    *Product: ${id.product_id.name}* <br>
                    *Qty: ${id.product_qty}* <br>
                    *Price: ${id.price_subtotal}*<br>
                % endfor
            % endfor
        </div>
    % endif
    <div>
        We are expecting receipt of products on or before scheduled date <br/>
        If you have any questions, please feel free to contact us.
    </div>
    </p>
</div>
                ]]>
            </field>
        </record>

        <!-- 10. Whats app Generic template (custom, if needed) -->
        <record id="mail_template_whatsapp_generic" model="mail.template">
            <field name="name">WhatsApp Generic Template</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="subject">Generic WhatsApp Template</field>
            <field name="body_html" type="html">
                <![CDATA[
<div style="font-family:'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif;font-size:12px;">
    Hello,<br><br>
    This is a template for WhatsApp messages.<br>
    Please customize this template as per your operational needs.
</div>
                ]]>
            </field>
        </record>
    </data>
</odoo>
