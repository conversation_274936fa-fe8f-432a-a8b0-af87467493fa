#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo Script: User Filtering in Tobacco Sales Report

This script demonstrates how the user filtering functionality works in the
tobacco sales report. It shows how to:

1. Create a report wizard with user selection
2. Filter reports by official vs test users
3. Generate reports that exclude test data

Usage:
    Run this script in Odoo shell or as a server action to see the functionality.
"""

def demo_user_filtering(env):
    """
    Demonstrate user filtering functionality.
    
    Args:
        env: Odoo environment object
    """
    print("=== Tobacco Sales Report User Filtering Demo ===\n")
    
    # Find the users
    official_user = env['res.users'].search([('login', '=', '<EMAIL>')], limit=1)
    test_user = env['res.users'].search([('login', '=', '<EMAIL>')], limit=1)
    
    print(f"Official User: {official_user.name if official_user else 'Not found'} ({official_user.login if official_user else 'N/A'})")
    print(f"Test User: {test_user.name if test_user else 'Not found'} ({test_user.login if test_user else 'N/A'})")
    print()
    
    # Create a wizard instance
    wizard = env['tobacco.sales.report.wizard'].create({
        'date_from': '2024-01-01',
        'date_to': '2024-12-31',
        'product_type': 'both',
    })
    
    print(f"Default user in wizard: {wizard.user_id.name if wizard.user_id else 'None'}")
    print()
    
    # Demonstrate filtering with official user
    if official_user:
        wizard.user_id = official_user
        print(f"Set wizard user to: {wizard.user_id.name} ({wizard.user_id.login})")
        
        # Create report
        report = env['tobacco.sales.report'].create({
            'date_from': wizard.date_from,
            'date_to': wizard.date_to,
            'user_id': wizard.user_id.id,
            'product_type': wizard.product_type,
        })
        
        # Collect data
        sales_data = report._collect_sales_data()
        print(f"Official user report found {len(sales_data)} invoice(s)")
        
        if sales_data:
            total_amount = sum(data['tobacco_sales_total'] + data['cigar_sales_total'] for data in sales_data)
            print(f"Total sales amount: ${total_amount:.2f}")
        print()
    
    # Demonstrate filtering with test user
    if test_user:
        wizard.user_id = test_user
        print(f"Set wizard user to: {wizard.user_id.name} ({wizard.user_id.login})")
        
        # Create report
        report = env['tobacco.sales.report'].create({
            'date_from': wizard.date_from,
            'date_to': wizard.date_to,
            'user_id': wizard.user_id.id,
            'product_type': wizard.product_type,
        })
        
        # Collect data
        sales_data = report._collect_sales_data()
        print(f"Test user report found {len(sales_data)} invoice(s)")
        
        if sales_data:
            total_amount = sum(data['tobacco_sales_total'] + data['cigar_sales_total'] for data in sales_data)
            print(f"Total sales amount: ${total_amount:.2f}")
        print()
    
    print("=== Demo Complete ===")
    print("\nKey Benefits:")
    print("• Official reports (<EMAIL>) exclude test data")
    print("• Test reports (<EMAIL>) show only test data")
    print("• Easy user selection in the wizard interface")
    print("• Automatic filtering by invoice creator (create_uid)")


# Example usage in Odoo shell:
# demo_user_filtering(env)
