# -*- coding: utf-8 -*-
"""
Product Template and Product Product models for cigarette and tobacco classification.

This module extends the base product models to provide category-based classification
for cigars, cigarettes, tobacco, and disposable products. It includes methods for
determining product types and generating sort keys for proper ordering in reports.
"""

from odoo import models


class ProductTemplate(models.Model):
    """
    Extends product.template to add cigarette and tobacco classification functionality.

    This class provides methods to classify products based on their category hierarchy
    and generate sort keys for proper ordering in sales orders and invoices.
    """
    _inherit = 'product.template'

    def is_cigar_category(self):
        """
        Check if product belongs to cigar category.

        First checks if the main parent category is 'tobacco' - if so, returns False
        to ensure tobacco products are classified as tobacco, not cigar.
        Then traverses the category hierarchy checking for 'cigar' keywords while
        explicitly excluding 'cigarette' to prevent classification conflicts.

        Returns:
            bool: True if product is in cigar category, False otherwise
        """
        # Return False if no category is assigned to the product
        if not self.categ_id:
            return False

        # First check if main parent is tobacco - if so, this should be tobacco, not cigar
        main_parent = self._get_main_parent_category()
        if main_parent and 'tobacco' in main_parent.name.lower():
            return False

        # Traverse category hierarchy from current category to root
        category = self.categ_id
        while category:
            category_name_lower = category.name.lower()
            # Check if current category name contains 'cigar' but NOT 'cigarette' (case-insensitive)
            # This prevents cigarette products from being classified as cigars
            if 'cigar' in category_name_lower and 'cigarette' not in category_name_lower:
                return True
            # Move to parent category
            category = category.parent_id
        return False

    def is_cigarette_category(self):
        """
        Check if product belongs to cigarette category.

        NOTE: CIGARETTE FUNCTIONALITY NOT YET IMPLEMENTED
        This method is currently disabled and will always return False.
        Implementation is planned for future releases.

        When implemented, it will first check if the main parent category is 'tobacco'
        - if so, returns False to ensure tobacco products are classified as tobacco, not cigarette.
        Then traverses the category hierarchy checking for 'cigarette' keywords.

        Returns:
            bool: True if product is in cigarette category, False otherwise
        """
        # TODO: CIGARETTE CLASSIFICATION NOT IMPLEMENTED
        # Temporarily return False until cigarette functionality is implemented
        return False

        # COMMENTED OUT - CIGARETTE IMPLEMENTATION PENDING
        # # Return False if no category is assigned to the product
        # if not self.categ_id:
        #     return False

        # # First check if main parent is tobacco - if so, this should be tobacco, not cigarette
        # main_parent = self._get_main_parent_category()
        # if main_parent and 'tobacco' in main_parent.name.lower():
        #     return False

        # # Traverse category hierarchy from current category to root
        # category = self.categ_id
        # while category:
        #     # Check if current category name contains 'cigarette' (case-insensitive)
        #     if 'cigarette' in category.name.lower():
        #         return True
        #     # Move to parent category
        #     category = category.parent_id
        # return False

    def _get_main_parent_category(self):
        """
        Get the main parent category (top-level category) for this product.

        Traverses up the category hierarchy to find the root category.
        This is used to determine the main classification of a product.

        Returns:
            product.category: The main parent category or None if no category
        """
        if not self.categ_id:
            return None

        # Traverse up to find the main parent (category with no parent)
        category = self.categ_id
        while category.parent_id:
            category = category.parent_id
        return category

    def is_tobacco_category(self):
        """
        Check if product belongs to tobacco category.

        Checks if the main parent category contains 'tobacco' or if any category
        in the hierarchy contains 'tobacco' (case-insensitive).

        Returns:
            bool: True if product is in tobacco category, False otherwise
        """
        # Return False if no category is assigned to the product
        if not self.categ_id:
            return False

        # Check if main parent is tobacco
        main_parent = self._get_main_parent_category()
        if main_parent and 'tobacco' in main_parent.name.lower():
            return True

        # Also check the full hierarchy for tobacco keywords
        category = self.categ_id
        while category:
            # Check if current category name contains 'tobacco' (case-insensitive)
            if 'tobacco' in category.name.lower():
                return True
            # Move to parent category
            category = category.parent_id
        return False

    def get_product_sort_key(self):
        """
        Return sort key for product ordering in reports and lists.

        The sorting order is designed to prioritize products as follows:
        1. Premium Cigars (highest priority)
        2. Cigarettes
        3. Tobacco Products
        4. Non Tobacco Products
        5. Disposable Products (lowest priority)

        Returns:
            int: Sort key (1-5) where lower numbers have higher priority
        """
        # Check product type and return appropriate sort key
        if self.is_cigar_category():
            return 1  # Premium cigars have highest priority
        # elif self.is_cigarette_category():
        #     return 2  # Cigarettes come second - NOT IMPLEMENTED YET
        elif self.is_tobacco_category():
            return 3  # Tobacco products come third (was second, moved up due to cigarette not implemented)
        elif self.is_disposable_category():
            return 5  # Disposable products have lowest priority
        else:
            return 4  # Non tobacco products come fourth

    def is_disposable_category(self):
        """
        Check if product belongs to disposable category.

        First checks if the main parent category is 'tobacco' - if so, returns False
        to ensure tobacco products are classified as tobacco, not disposable.
        Then traverses the category hierarchy checking for 'disposable' keywords.

        Returns:
            bool: True if product is in disposable category, False otherwise
        """
        # Return False if no category is assigned to the product
        if not self.categ_id:
            return False

        # First check if main parent is tobacco - if so, this should be tobacco, not disposable
        main_parent = self._get_main_parent_category()
        if main_parent and 'tobacco' in main_parent.name.lower():
            return False

        # Traverse category hierarchy from current category to root
        category = self.categ_id
        while category:
            # Check if current category name contains 'disposable' (case-insensitive)
            if 'disposable' in category.name.lower():
                return True
            # Move to parent category
            category = category.parent_id
        return False

    def debug_category_info(self):
        """
        Debug method to display complete category hierarchy for troubleshooting.

        This method is useful for debugging category classification issues
        by showing the complete path from current category to root, plus the main parent.

        Returns:
            str: Human-readable category hierarchy or "No category assigned"
        """
        # Handle case where no category is assigned
        if not self.categ_id:
            return "No category assigned"

        # Get main parent category
        main_parent = self._get_main_parent_category()
        main_parent_info = f"Main parent: {main_parent.name} (ID: {main_parent.id})" if main_parent else "No main parent"

        # Build list of category names with IDs from current to root
        categories = []
        category = self.categ_id
        while category:
            # Add category name and ID to the list
            categories.append(f"{category.name} (ID: {category.id})")
            # Move to parent category
            category = category.parent_id

        # Return formatted hierarchy string with main parent info
        return f"{main_parent_info} | Category hierarchy: {' -> '.join(categories)}"


class ProductProduct(models.Model):
    """
    Extends product.product to add cigarette and tobacco classification functionality.

    This class provides the same classification methods as ProductTemplate but for
    product variants. It includes additional methods for getting human-readable
    product type names for use in reports and user interfaces.
    """
    _inherit = 'product.product'

    def _get_main_parent_category(self):
        """
        Get the main parent category (top-level category) for this product.

        Traverses up the category hierarchy to find the root category.
        This is used to determine the main classification of a product.

        Returns:
            product.category: The main parent category or None if no category
        """
        if not self.categ_id:
            return None

        # Traverse up to find the main parent (category with no parent)
        category = self.categ_id
        while category.parent_id:
            category = category.parent_id
        return category

    def is_cigar_category(self):
        """
        Check if product belongs to cigar category.

        First checks if the main parent category is 'tobacco' - if so, returns False
        to ensure tobacco products are classified as tobacco, not cigar.
        Then traverses the category hierarchy checking for 'cigar' keywords while
        explicitly excluding 'cigarette' to prevent classification conflicts.

        Returns:
            bool: True if product is in cigar category, False otherwise
        """
        # Return False if no category is assigned to the product
        if not self.categ_id:
            return False

        # First check if main parent is tobacco - if so, this should be tobacco, not cigar
        main_parent = self._get_main_parent_category()
        if main_parent and 'tobacco' in main_parent.name.lower():
            return False

        # Traverse category hierarchy from current category to root
        category = self.categ_id
        while category:
            category_name_lower = category.name.lower()
            # Check if current category name contains 'cigar' but NOT 'cigarette' (case-insensitive)
            # This prevents cigarette products from being classified as cigars
            if 'cigar' in category_name_lower and 'cigarette' not in category_name_lower:
                return True
            # Move to parent category
            category = category.parent_id
        return False

    def is_cigarette_category(self):
        """
        Check if product belongs to cigarette category.

        NOTE: CIGARETTE FUNCTIONALITY NOT YET IMPLEMENTED
        This method is currently disabled and will always return False.
        Implementation is planned for future releases.

        When implemented, it will first check if the main parent category is 'tobacco'
        - if so, returns False to ensure tobacco products are classified as tobacco, not cigarette.
        Then traverses the category hierarchy checking for 'cigarette' keywords.

        Returns:
            bool: True if product is in cigarette category, False otherwise
        """
        # TODO: CIGARETTE CLASSIFICATION NOT IMPLEMENTED
        # Temporarily return False until cigarette functionality is implemented
        return False

        # COMMENTED OUT - CIGARETTE IMPLEMENTATION PENDING
        # # Return False if no category is assigned to the product
        # if not self.categ_id:
        #     return False

        # # First check if main parent is tobacco - if so, this should be tobacco, not cigarette
        # main_parent = self._get_main_parent_category()
        # if main_parent and 'tobacco' in main_parent.name.lower():
        #     return False

        # # Traverse category hierarchy from current category to root
        # category = self.categ_id
        # while category:
        #     # Check if current category name contains 'cigarette' (case-insensitive)
        #     if 'cigarette' in category.name.lower():
        #         return True
        #     # Move to parent category
        #     category = category.parent_id
        # return False

    def is_tobacco_category(self):
        """
        Check if product belongs to tobacco category.

        Checks if the main parent category contains 'tobacco' or if any category
        in the hierarchy contains 'tobacco' (case-insensitive).

        Returns:
            bool: True if product is in tobacco category, False otherwise
        """
        # Return False if no category is assigned to the product
        if not self.categ_id:
            return False

        # Check if main parent is tobacco
        main_parent = self._get_main_parent_category()
        if main_parent and 'tobacco' in main_parent.name.lower():
            return True

        # Also check the full hierarchy for tobacco keywords
        category = self.categ_id
        while category:
            # Check if current category name contains 'tobacco' (case-insensitive)
            if 'tobacco' in category.name.lower():
                return True
            # Move to parent category
            category = category.parent_id
        return False

    def get_product_sort_key(self):
        """
        Return sort key for product ordering in reports and lists.

        The sorting order is designed to prioritize products as follows:
        1. Premium Cigars (highest priority)
        2. Cigarettes
        3. Tobacco Products
        4. Non Tobacco Products
        5. Disposable Products (lowest priority)

        Returns:
            int: Sort key (1-5) where lower numbers have higher priority
        """
        # Check product type and return appropriate sort key
        if self.is_cigar_category():
            return 1  # Premium cigars have highest priority
        # elif self.is_cigarette_category():
        #     return 2  # Cigarettes come second - NOT IMPLEMENTED YET
        elif self.is_tobacco_category():
            return 3  # Tobacco products come third (was second, moved up due to cigarette not implemented)
        elif self.is_disposable_category():
            return 5  # Disposable products have lowest priority
        else:
            return 4  # Non tobacco products come fourth

    def is_disposable_category(self):
        """
        Check if product belongs to disposable category.

        First checks if the main parent category is 'tobacco' - if so, returns False
        to ensure tobacco products are classified as tobacco, not disposable.
        Then traverses the category hierarchy checking for 'disposable' keywords.

        Returns:
            bool: True if product is in disposable category, False otherwise
        """
        # Return False if no category is assigned to the product
        if not self.categ_id:
            return False

        # First check if main parent is tobacco - if so, this should be tobacco, not disposable
        main_parent = self._get_main_parent_category()
        if main_parent and 'tobacco' in main_parent.name.lower():
            return False

        # Traverse category hierarchy from current category to root
        category = self.categ_id
        while category:
            # Check if current category name contains 'disposable' (case-insensitive)
            if 'disposable' in category.name.lower():
                return True
            # Move to parent category
            category = category.parent_id
        return False

    def get_product_type_name(self):
        """
        Return human-readable product type name for grouping in reports.

        This method provides user-friendly names for product categories that
        can be used in reports, user interfaces, and grouping operations.

        Returns:
            str: Human-readable product type name
        """
        # Return appropriate type name based on product category
        if self.is_cigar_category():
            return 'Premium Cigars'
        # elif self.is_cigarette_category():
        #     return 'Cigarettes'  # NOT IMPLEMENTED YET
        elif self.is_tobacco_category():
            return 'Tobacco Products'
        elif self.is_disposable_category():
            return 'Disposable Products'
        else:
            return 'Non Tobacco Products'

    def debug_category_info(self):
        """
        Debug method to display complete category hierarchy for troubleshooting.

        This method is useful for debugging category classification issues
        by showing the complete path from current category to root, plus the main parent.

        Returns:
            str: Human-readable category hierarchy or "No category assigned"
        """
        # Handle case where no category is assigned
        if not self.categ_id:
            return "No category assigned"

        # Get main parent category
        main_parent = self._get_main_parent_category()
        main_parent_info = f"Main parent: {main_parent.name} (ID: {main_parent.id})" if main_parent else "No main parent"

        # Build list of category names with IDs from current to root
        categories = []
        category = self.categ_id
        while category:
            # Add category name and ID to the list
            categories.append(f"{category.name} (ID: {category.id})")
            # Move to parent category
            category = category.parent_id

        # Return formatted hierarchy string with main parent info
        return f"{main_parent_info} | Category hierarchy: {' -> '.join(categories)}"
