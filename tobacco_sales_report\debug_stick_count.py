#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Script for Cigar Stick Count Calculation

This script helps debug why the automatic stick count calculation might not be working.
Run this in the Odoo shell or as a server action to diagnose issues.

Usage in Odoo shell:
python3 odoo-bin shell -d your_database_name
>>> exec(open('debug_stick_count.py').read())
"""

def debug_stick_count_calculation():
    """
    Debug function to check stick count calculation for cigar products.
    """
    print("=== CIGAR STICK COUNT DEBUG ===")
    
    # Get environment
    env = globals().get('env')
    if not env:
        print("ERROR: This script must be run in Odoo shell environment")
        return
    
    print("\n1. Checking for cigar products...")
    
    # Find products that should be cigars
    products = env['product.product'].search([])
    cigar_products = []
    
    for product in products:
        if hasattr(product, 'is_cigar_category') and product.is_cigar_category():
            cigar_products.append(product)
            print(f"   ✓ Found cigar product: {product.name} (ID: {product.id})")
            
            # Check for custom fields
            x_cigars_per_box = getattr(product, 'x_cigars_per_box', None)
            x_sticks_per_unit = getattr(product, 'x_sticks_per_unit', None)
            
            print(f"     - x_cigars_per_box: {x_cigars_per_box}")
            print(f"     - x_sticks_per_unit: {x_sticks_per_unit}")
    
    if not cigar_products:
        print("   ❌ No cigar products found!")
        print("   Check if products are properly categorized as cigars")
        return
    
    print(f"\n2. Found {len(cigar_products)} cigar product(s)")
    
    # Test with first cigar product
    test_product = cigar_products[0]
    print(f"\n3. Testing calculation with: {test_product.name}")
    
    # Create a test sale order line (in memory, not saved)
    sale_line = env['sale.order.line'].new({
        'product_id': test_product.id,
        'product_uom_qty': 2,
    })
    
    print(f"   - Product: {test_product.name}")
    print(f"   - Quantity: 2")
    
    # Check if stick count field exists
    has_stick_field = hasattr(sale_line, 'x_cigar_stick_count')
    print(f"   - Has x_cigar_stick_count field: {has_stick_field}")
    
    if not has_stick_field:
        print("   ❌ x_cigar_stick_count field not found!")
        print("   You need to create this field in Odoo Studio on sale.order.line")
        return
    
    # Test calculation
    calculated = sale_line.calculate_cigar_stick_count()
    print(f"   - Calculated stick count: {calculated}")
    
    # Test force calculation
    forced = sale_line.force_calculate_stick_count()
    print(f"   - Force calculated: {forced}")
    print(f"   - Field value after force: {getattr(sale_line, 'x_cigar_stick_count', 'N/A')}")
    
    print("\n4. Field existence check:")
    
    # Check if fields exist in database
    fields_to_check = [
        ('product.product', 'x_cigars_per_box'),
        ('product.product', 'x_sticks_per_unit'),
        ('sale.order.line', 'x_cigar_stick_count'),
        ('account.move.line', 'x_cigar_stick_count'),
    ]
    
    for model_name, field_name in fields_to_check:
        field_exists = env['ir.model.fields'].search([
            ('model', '=', model_name),
            ('name', '=', field_name)
        ])
        status = "✓ EXISTS" if field_exists else "❌ MISSING"
        print(f"   {model_name}.{field_name}: {status}")
    
    print("\n=== DEBUG COMPLETE ===")
    
    if not has_stick_field:
        print("\n🔧 SOLUTION:")
        print("1. Go to Settings → Technical → Database Structure → Fields")
        print("2. Create field 'x_cigar_stick_count' on 'sale.order.line'")
        print("3. Create field 'x_cigar_stick_count' on 'account.move.line'")
        print("4. Make sure your cigar products have x_cigars_per_box = 20")


# Run the debug function
if __name__ == "__main__":
    debug_stick_count_calculation()
else:
    # If imported, make function available
    print("Debug function loaded. Call debug_stick_count_calculation() to run.")
