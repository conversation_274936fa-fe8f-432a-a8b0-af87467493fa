# -*- coding: utf-8 -*-
"""
Sale Order Line Extension for Tobacco Sales Reporting

This module provides utility methods for working with cigar stick count
on sale order lines. The x_cigar_stick_count field must be created via
Odoo Studio to avoid any interference with accounting calculations.

NO FIELD DEFINITIONS - Fields must be created via Studio only!
"""

from odoo import models, api


class SaleOrderLine(models.Model):
    """
    Extends sale.order.line to provide utility methods for cigar stick count.

    This extension only provides helper methods and does NOT define any fields.
    The x_cigar_stick_count field must be created via Odoo Studio.
    """
    _inherit = 'sale.order.line'

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        Returns:
            int: The stick count value, or 0 if field doesn't exist
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def calculate_cigar_stick_count(self):
        """
        Calculate the stick count based on product configuration.

        Returns:
            int: Calculated stick count
        """
        if not (self.product_id and hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        # Try to find a custom field that indicates sticks per unit
        sticks_per_unit = (
            getattr(self.product_id, 'x_sticks_per_unit', None) or
            getattr(self.product_id, 'x_stick_count', None) or
            getattr(self.product_id, 'x_pieces_per_box', None) or
            getattr(self.product_id, 'x_cigars_per_box', None) or
            1  # Default to 1 if no custom field found
        )

        return int(self.product_uom_qty * sticks_per_unit)

    @api.onchange('product_id', 'product_uom_qty')
    def _onchange_product_auto_calculate_stick_count(self):
        """
        Auto-calculate stick count when product or quantity changes.

        Only auto-fills if current stick count is 0 to allow manual override.
        """
        # Debug: Check if we have the basic requirements
        if not self.product_id:
            return

        # Check if product has cigar category method and is a cigar
        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return

        # Check if we have the stick count field (might not exist if not created in Studio)
        if not hasattr(self, 'x_cigar_stick_count'):
            # Field doesn't exist yet - this is expected if not created in Studio
            return

        # Auto-calculate stick count
        # Always recalculate when product or quantity changes
        calculated_count = self.calculate_cigar_stick_count()
        if calculated_count > 0:
            # Check if this looks like an auto-calculated value vs manual override
            current_stick_count = getattr(self, 'x_cigar_stick_count', 0)

            # Get the sticks per unit to check if current value matches a calculation
            sticks_per_unit = (
                getattr(self.product_id, 'x_sticks_per_unit', None) or
                getattr(self.product_id, 'x_stick_count', None) or
                getattr(self.product_id, 'x_pieces_per_box', None) or
                getattr(self.product_id, 'x_cigars_per_box', None) or
                1
            )

            # If current value is 0 or looks like it was auto-calculated, update it
            # This allows recalculation when quantity changes but preserves true manual edits
            if (current_stick_count == 0 or
                current_stick_count == int((self.product_uom_qty or 0) * sticks_per_unit) or
                current_stick_count % sticks_per_unit == 0):  # Looks like a multiple
                self.x_cigar_stick_count = calculated_count

    def force_calculate_stick_count(self):
        """
        Force calculate and set stick count regardless of current value.

        This method can be used for testing or manual recalculation.
        Returns the calculated count for debugging.
        """
        if not self.product_id:
            return 0

        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        calculated_count = self.calculate_cigar_stick_count()

        # Set the value if field exists
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = calculated_count

        return calculated_count
