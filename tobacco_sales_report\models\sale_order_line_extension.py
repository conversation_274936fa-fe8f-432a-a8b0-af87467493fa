# -*- coding: utf-8 -*-
"""
Sale Order Line Extension for Tobacco Sales Reporting

This module provides utility methods for working with cigar stick count
on sale order lines. The x_cigar_stick_count field must be created via
Odoo Studio to avoid any interference with accounting calculations.

NO FIELD DEFINITIONS - Fields must be created via Studio only!
"""

from odoo import models, api


class SaleOrderLine(models.Model):
    """
    Extends sale.order.line to provide utility methods for cigar stick count.

    This extension only provides helper methods and does NOT define any fields.
    The x_cigar_stick_count field must be created via Odoo Studio.
    """
    _inherit = 'sale.order.line'

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        Returns:
            int: The stick count value, or 0 if field doesn't exist
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def calculate_cigar_stick_count(self):
        """
        Calculate the stick count based on product configuration.

        Returns:
            int: Calculated stick count
        """
        import logging
        _logger = logging.getLogger(__name__)

        if not (self.product_id and hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            _logger.info("CALC: Product is not cigar category")
            return 0

        # Try to find a custom field that indicates sticks per unit
        x_sticks_per_unit = getattr(self.product_id, 'x_sticks_per_unit', None)
        x_stick_count = getattr(self.product_id, 'x_stick_count', None)
        x_pieces_per_box = getattr(self.product_id, 'x_pieces_per_box', None)
        x_cigars_per_box = getattr(self.product_id, 'x_cigars_per_box', None)

        _logger.info(f"CALC: Product fields - sticks_per_unit={x_sticks_per_unit}, stick_count={x_stick_count}, pieces_per_box={x_pieces_per_box}, cigars_per_box={x_cigars_per_box}")

        sticks_per_unit = (
            x_sticks_per_unit or
            x_stick_count or
            x_pieces_per_box or
            x_cigars_per_box or
            1  # Default to 1 if no custom field found
        )

        _logger.info(f"CALC: Using sticks_per_unit={sticks_per_unit}, quantity={self.product_uom_qty}")
        result = int(self.product_uom_qty * sticks_per_unit)
        _logger.info(f"CALC: Result = {result}")

        return result

    @api.onchange('product_id', 'product_uom_qty')
    def _onchange_product_auto_calculate_stick_count(self):
        """
        Auto-calculate stick count when product or quantity changes.
        """
        # Debug logging - remove this after testing
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"ONCHANGE TRIGGERED: product={self.product_id.name if self.product_id else 'None'}, qty={self.product_uom_qty}")

        # Debug: Check if we have the basic requirements
        if not self.product_id:
            _logger.info("ONCHANGE: No product selected")
            return

        # Check if product has cigar category method and is a cigar
        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            _logger.info(f"ONCHANGE: Product {self.product_id.name} is not a cigar category")
            return

        # Check if we have the stick count field (might not exist if not created in Studio)
        if not hasattr(self, 'x_cigar_stick_count'):
            _logger.info("ONCHANGE: x_cigar_stick_count field doesn't exist")
            return

        # Auto-calculate stick count - always recalculate when quantity changes
        calculated_count = self.calculate_cigar_stick_count()
        _logger.info(f"ONCHANGE: Calculated count = {calculated_count}")

        if calculated_count > 0:
            old_value = getattr(self, 'x_cigar_stick_count', 0)
            self.x_cigar_stick_count = calculated_count
            _logger.info(f"ONCHANGE: Updated stick count from {old_value} to {calculated_count}")
        else:
            _logger.info("ONCHANGE: Calculated count is 0, not updating")

    def force_calculate_stick_count(self):
        """
        Force calculate and set stick count regardless of current value.

        This method can be used for testing or manual recalculation.
        Returns the calculated count for debugging.
        """
        if not self.product_id:
            return 0

        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        calculated_count = self.calculate_cigar_stick_count()

        # Set the value if field exists
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = calculated_count

        return calculated_count

    def debug_stick_count_info(self):
        """
        Debug method to show all relevant information for stick count calculation.
        Call this method to see what's happening.
        """
        info = []
        info.append(f"=== STICK COUNT DEBUG INFO ===")
        info.append(f"Product: {self.product_id.name if self.product_id else 'None'}")
        info.append(f"Quantity: {self.product_uom_qty}")

        if self.product_id:
            info.append(f"Is cigar category: {hasattr(self.product_id, 'is_cigar_category') and self.product_id.is_cigar_category()}")
            info.append(f"x_cigars_per_box: {getattr(self.product_id, 'x_cigars_per_box', 'MISSING')}")
            info.append(f"x_sticks_per_unit: {getattr(self.product_id, 'x_sticks_per_unit', 'MISSING')}")

        info.append(f"Has stick count field: {hasattr(self, 'x_cigar_stick_count')}")
        if hasattr(self, 'x_cigar_stick_count'):
            info.append(f"Current stick count: {getattr(self, 'x_cigar_stick_count', 'ERROR')}")

        calculated = self.calculate_cigar_stick_count()
        info.append(f"Calculated stick count: {calculated}")

        return "\n".join(info)
