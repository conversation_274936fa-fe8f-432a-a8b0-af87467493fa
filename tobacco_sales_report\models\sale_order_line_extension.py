# -*- coding: utf-8 -*-
"""
Sale Order Line Extension for Tobacco Sales Reporting

This module provides utility methods for working with cigar stick count
on sale order lines. The x_cigar_stick_count field must be created via
Odoo Studio to avoid any interference with accounting calculations.

NO FIELD DEFINITIONS - Fields must be created via Studio only!
"""

from odoo import models, fields, api


class SaleOrderLine(models.Model):
    """
    Extends sale.order.line to provide cigar stick count functionality.

    This extension provides the x_cigar_stick_count field and helper methods
    for automatic calculation and manual override of stick counts.
    """
    _inherit = 'sale.order.line'

    x_cigar_stick_count = fields.Integer(
        string='Cigar Sticks',
        help='Number of individual cigar sticks being sold. Auto-calculated based on product configuration but can be manually overridden.',
        default=0
    )

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        Returns:
            int: The stick count value, or 0 if field doesn't exist
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def calculate_cigar_stick_count(self):
        """
        Calculate the stick count based on product configuration.

        Returns:
            int: Calculated stick count
        """
        if not (self.product_id and hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        # Try to find a custom field that indicates sticks per unit
        sticks_per_unit = (
            getattr(self.product_id, 'x_sticks_per_unit', None) or
            getattr(self.product_id, 'x_stick_count', None) or
            getattr(self.product_id, 'x_pieces_per_box', None) or
            getattr(self.product_id, 'x_cigars_per_box', None) or
            1  # Default to 1 if no custom field found
        )

        return int(self.product_uom_qty * sticks_per_unit)

    @api.onchange('product_id', 'product_uom_qty')
    def _onchange_product_auto_calculate_stick_count(self):
        """
        Auto-calculate stick count when product or quantity changes.

        Only auto-fills if current stick count is 0 to allow manual override.
        """
        if (hasattr(self, 'x_cigar_stick_count') and
            self.product_id and
            hasattr(self.product_id, 'is_cigar_category') and
            self.product_id.is_cigar_category()):

            # Only auto-calculate if current value is 0 (allows manual override)
            current_stick_count = getattr(self, 'x_cigar_stick_count', 0)
            if current_stick_count == 0:
                calculated_count = self.calculate_cigar_stick_count()
                if calculated_count > 0:
                    self.x_cigar_stick_count = calculated_count
