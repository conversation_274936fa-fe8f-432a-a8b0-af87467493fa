# Omnisol Website - Omtech Products

A professional website module for Odoo 18 showcasing Omnisol's Omtech product line.

## Overview

This module creates a complete website solution for Omnisol to showcase their Omtech laser cutting and engraving equipment. The website features a modern, professional design with responsive layout and comprehensive product information.

## Features

### 🏠 Home Page
- Hero section with compelling messaging
- Company statistics and achievements
- Featured products showcase
- Professional call-to-action sections
- Customer testimonials area

### 🛠️ Products Page
- Comprehensive product catalog
- Detailed product specifications
- Product comparison table
- Professional product photography placeholders
- Category-based organization

### 📄 Additional Pages
- About page with company information
- Contact page with form and details
- Professional navigation structure
- SEO-optimized content

### 🎨 Design Features
- Modern, professional theme
- Responsive design for all devices
- Custom CSS with professional styling
- Interactive JavaScript features
- Professional color scheme
- Smooth animations and transitions

## Product Categories

### Entry Level
- **Omtech K40 Laser Cutter**
  - 40W CO2 laser tube
  - 300mm x 200mm cutting area
  - Perfect for small businesses and hobbyists
  - Starting at $899

### Professional
- **Omtech 80W CO2 Laser**
  - 80W CO2 laser tube
  - 700mm x 500mm cutting area
  - Professional-grade features
  - Starting at $2,499

### Industrial
- **Omtech 150W Industrial Laser**
  - 150W CO2 laser tube
  - 1300mm x 900mm cutting area
  - Heavy-duty industrial use
  - Starting at $4,999

## Installation

1. Copy the `omnisol_website` folder to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Omnisol Website - Omtech Products" module
4. The website will be automatically configured

## Technical Details

### Dependencies
- `base` - Odoo base module
- `website` - Website builder
- `website_sale` - E-commerce features
- `portal` - Customer portal

### File Structure
```
omnisol_website/
├── __init__.py
├── __manifest__.py
├── README.md
├── controllers/
│   ├── __init__.py
│   └── main.py
├── data/
│   ├── website_data.xml
│   └── website_menu.xml
├── views/
│   ├── website_templates.xml
│   ├── home_page.xml
│   └── product_page.xml
└── static/
    ├── description/
    │   └── index.html
    └── src/
        ├── css/
        │   └── omnisol_style.css
        ├── js/
        │   └── omnisol_website.js
        └── img/
            └── (product images)
```

### Customization

The module is designed to be easily customizable:

- **Colors**: Modify CSS variables in `omnisol_style.css`
- **Content**: Update XML templates in the `views/` directory
- **Images**: Replace placeholder images in `static/src/img/`
- **Branding**: Update logo and company information

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Performance

- Optimized CSS and JavaScript
- Lazy loading for images
- Responsive design
- SEO-friendly structure
- Fast loading times

## Support

For technical support or customization requests, please contact the development team.

## Version History

- **********.0** - Initial release
  - Professional home page
  - Comprehensive product showcase
  - Responsive design
  - SEO optimization

## License

LGPL-3 License

---

**Omnisol Website Module for Odoo 18**  
Professional Omtech Solutions Showcase
