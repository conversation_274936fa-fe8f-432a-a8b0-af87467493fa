# -*- coding: utf-8 -*-
"""
Models package initialization for cigarette and tobacco classification module.

This package contains model extensions that add product classification and sorting
functionality for tobacco-related products in Odoo. The models extend core Odoo
functionality to provide category-based classification and business-priority sorting.

Note: Cigarette classification methods are present but currently disabled/not implemented.

Modules included:
- product_template: Product classification methods for templates and variants
- sale_order: Sales order and line sorting functionality
- account_move: Invoice and invoice line sorting functionality
"""

# Import all model extensions
from . import product_template  # Product classification methods
from . import sale_order       # Sales order sorting functionality
from . import account_move     # Invoice sorting functionality
